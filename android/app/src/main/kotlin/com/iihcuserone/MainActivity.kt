package com.iihcuserone

import android.content.Intent
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.iihcuserone/fcm"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        // إعداد Method Channel للتواصل مع Flutter
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getInitialIntent" -> {
                    val initialIntent = intent
                    val intentData = extractIntentData(initialIntent)
                    result.success(intentData)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // معالجة Intent عند فتح التطبيق من إشعار
        handleIntent(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        
        // معالجة Intent الجديد
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        intent?.let {
            // التحقق من وجود بيانات FCM في Intent
            val extras = it.extras
            if (extras != null) {
                for (key in extras.keySet()) {
                    val value = extras.get(key)
                    println("FCM Intent Extra: $key = $value")
                }
            }
        }
    }

    private fun extractIntentData(intent: Intent?): Map<String, Any>? {
        intent?.let {
            val extras = it.extras
            if (extras != null) {
                val data = mutableMapOf<String, Any>()
                for (key in extras.keySet()) {
                    val value = extras.get(key)
                    if (value != null) {
                        data[key] = value
                    }
                }
                return data
            }
        }
        return null
    }
}