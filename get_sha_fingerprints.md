# الحصول على SHA Certificate Fingerprints

## للحصول على SHA1 و SHA256 من مفتاح Release:

### الطريقة الأولى - من ملف keystore:
```bash
keytool -list -v -keystore android/app/iihc-release-key.jks -alias upload
```

### الطريقة الثانية - من <PERSON>radle:
```bash
cd android
./gradlew signingReport
```

### الطريقة الثالثة - من Android Studio:
1. افتح Android Studio
2. اذهب إلى Gradle → android → signingReport
3. شغل المهمة وستحصل على جميع SHA fingerprints

## إضافة SHA fingerprints في Firebase:

1. اذهب إلى Firebase Console
2. اختر مشروعك (iihc-8841e)
3. اذهب إلى Project Settings
4. اختر تطبيق Android
5. أضف SHA certificate fingerprints:
   - SHA1 (للتوافق مع الإصدارات القديمة)
   - SHA256 (الأساسي)

## ملاحظة مهمة:
- تأكد من إضافة SHA fingerprints لكل من Debug و Release
- Debug SHA1: يمكن الحصول عليه من debug keystore
- Release SHA1 & SHA256: من ملف iihc-release-key.jks
