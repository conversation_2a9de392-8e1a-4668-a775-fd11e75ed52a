/// ثوابت طبية للترجمة من الإنجليزية إلى العربية
class MedicalConstants {
  /// أنواع العلاج
  static const Map<String, String> treatmentTypes = {
    'hearing': 'فحص السمع',
    'speech': 'علاج النطق',
    'behavior': 'تعديل السلوك',
    'adhd': 'علاج فرط الحركة وتشتت الانتباه',
    'autism': 'علاج طيف التوحد',
    'learning': 'علاج صعوبات التعلم',
    'cognitive': 'علاج معرفي',
    'social': 'تدريب اجتماعي',
    'motor': 'علاج حركي',
    'sensory': 'علاج حسي',
    'developmental': 'علاج تطويري',
    'communication': 'علاج التواصل',
    'therapy': 'علاج طبيعي',
    'physiotherapy': 'علاج طبيعي',
    'occupational': 'علاج وظيفي',
    'psychological': 'علاج نفسي',
    'nutrition': 'تغذية علاجية',
    'consultation': 'استشارة',
    'follow_up': 'متابعة',
    'assessment': 'تقييم',
    'rehabilitation': 'تأهيل',
  };

  /// أنواع الفحوصات
  static const Map<String, String> examinationTypes = {
    'x-ray': 'أشعة سينية',
    'xray': 'أشعة سينية',
    'x_ray': 'أشعة سينية',
    'blood': 'تحليل دم',
    'blood_test': 'تحليل دم',
    'blood test': 'تحليل دم',
    'ultrasound': 'موجات صوتية',
    'ultra_sound': 'موجات صوتية',
    'ultra sound': 'موجات صوتية',
    'mri': 'رنين مغناطيسي',
    'magnetic resonance': 'رنين مغناطيسي',
    'ct': 'أشعة مقطعية',
    'ct_scan': 'أشعة مقطعية',
    'ct scan': 'أشعة مقطعية',
    'computed tomography': 'أشعة مقطعية',
    'ecg': 'رسم قلب',
    'ekg': 'رسم قلب',
    'electrocardiogram': 'رسم قلب',
    'eeg': 'رسم مخ',
    'electroencephalogram': 'رسم مخ',
    'hearing_test': 'فحص السمع',
    'hearing test': 'فحص السمع',
    'audiometry': 'فحص السمع',
    'vision_test': 'فحص النظر',
    'vision test': 'فحص النظر',
    'eye_test': 'فحص النظر',
    'eye test': 'فحص النظر',
    'urine': 'تحليل بول',
    'urine_test': 'تحليل بول',
    'urine test': 'تحليل بول',
    'urinalysis': 'تحليل بول',
    'stool': 'تحليل براز',
    'stool_test': 'تحليل براز',
    'stool test': 'تحليل براز',
    'biopsy': 'خزعة',
    'endoscopy': 'منظار',
    'colonoscopy': 'منظار القولون',
    'mammography': 'أشعة الثدي',
    'mammogram': 'أشعة الثدي',
    'bone_density': 'كثافة العظام',
    'bone density': 'كثافة العظام',
    'dexa': 'كثافة العظام',
    'dexa_scan': 'كثافة العظام',
    'stress_test': 'اختبار الإجهاد',
    'stress test': 'اختبار الإجهاد',
    'exercise_test': 'اختبار الإجهاد',
    'allergy_test': 'اختبار الحساسية',
    'allergy test': 'اختبار الحساسية',
    'skin_test': 'اختبار الحساسية',
    'genetic_test': 'فحص جيني',
    'genetic test': 'فحص جيني',
    'dna_test': 'فحص جيني',
    'lab_test': 'فحص مختبري',
    'lab test': 'فحص مختبري',
    'laboratory': 'فحص مختبري',
    'clinical_exam': 'فحص إكلينيكي',
    'clinical exam': 'فحص إكلينيكي',
    'clinical': 'فحص إكلينيكي',
    'physical_exam': 'فحص جسدي',
    'physical exam': 'فحص جسدي',
    'physical': 'فحص جسدي',
    'neurological': 'فحص عصبي',
    'neuro': 'فحص عصبي',
    'neurological_exam': 'فحص عصبي',
    'cardiac': 'فحص قلبي',
    'heart': 'فحص قلبي',
    'cardiac_exam': 'فحص قلبي',
    'respiratory': 'فحص تنفسي',
    'lung': 'فحص تنفسي',
    'pulmonary': 'فحص تنفسي',
    'dermatological': 'فحص جلدي',
    'skin': 'فحص جلدي',
    'dermatology': 'فحص جلدي',
    'ophthalmological': 'فحص عيون',
    'eye': 'فحص عيون',
    'ophthalmology': 'فحص عيون',
    'dental': 'فحص أسنان',
    'teeth': 'فحص أسنان',
    'oral': 'فحص أسنان',
    'orthopedic': 'فحص عظام',
    'bone': 'فحص عظام',
    'joint': 'فحص عظام',
    'musculoskeletal': 'فحص عظام',
    'psychiatric': 'فحص نفسي',
    'mental': 'فحص نفسي',
    'psychology': 'فحص نفسي',
    'gynecological': 'فحص نسائي',
    'gynecology': 'فحص نسائي',
    'women': 'فحص نسائي',
    'pediatric': 'فحص أطفال',
    'pediatrics': 'فحص أطفال',
    'child': 'فحص أطفال',
    'children': 'فحص أطفال',
    'geriatric': 'فحص مسنين',
    'elderly': 'فحص مسنين',
    'senior': 'فحص مسنين',
    'general': 'فحص عام',
    'general_exam': 'فحص عام',
    'checkup': 'فحص عام',
    'routine': 'فحص عام',
    'consultation': 'استشارة',
    'consult': 'استشارة',
    'follow_up': 'متابعة',
    'follow up': 'متابعة',
    'followup': 'متابعة',
    'assessment': 'تقييم',
    'evaluation': 'تقييم',
    'screening': 'فحص وقائي',
    'diagnosis': 'تشخيص',
    'diagnostic': 'تشخيص',
    'behavioral': 'فحص سلوكي',
    'behavior': 'فحص سلوكي',
    'cognitive': 'فحص معرفي',
    'communication': 'فحص التواصل',
    'developmental': 'فحص تطويري',
    'hearing': 'فحص السمع',
    'motor': 'فحص حركي',
    'sensory': 'فحص حسي',
    'speech': 'فحص النطق',
    'other': 'فحص آخر',
  };

  /// حالات الواجبات
  static const Map<String, String> homeworkStatuses = {
    'assigned': 'مُكلف',
    'in_progress': 'قيد التنفيذ',
    'completed': 'مكتمل',
    'overdue': 'متأخر',
    'cancelled': 'ملغي',
    'pending': 'في الانتظار',
    'reviewed': 'تم المراجعة',
    'approved': 'معتمد',
    'rejected': 'مرفوض',
  };

  /// أولويات الواجبات
  static const Map<String, String> homeworkPriorities = {
    'low': 'منخفضة',
    'medium': 'متوسطة',
    'high': 'عالية',
    'urgent': 'عاجلة',
    'critical': 'حرجة',
  };

  /// أنواع الواجبات
  static const Map<String, String> homeworkTypes = {
    'exercise': 'تمرين',
    'reading': 'قراءة',
    'practice': 'ممارسة',
    'assessment': 'تقييم',
    'therapy': 'علاج',
    'medication': 'دواء',
    'diet': 'نظام غذائي',
    'lifestyle': 'نمط حياة',
    'follow_up': 'متابعة',
    'consultation': 'استشارة',
  };

  /// الحصول على النص العربي لنوع العلاج
  static String getTreatmentTypeText(String type) {
    return treatmentTypes[type.toLowerCase()] ?? type;
  }

  /// الحصول على النص العربي لنوع الفحص
  static String getExaminationTypeText(String type) {
    return examinationTypes[type.toLowerCase()] ?? type;
  }

  /// الحصول على النص العربي لحالة الواجب
  static String getHomeworkStatusText(String status) {
    return homeworkStatuses[status.toLowerCase()] ?? status;
  }

  /// الحصول على النص العربي لأولوية الواجب
  static String getHomeworkPriorityText(String priority) {
    return homeworkPriorities[priority.toLowerCase()] ?? priority;
  }

  /// الحصول على النص العربي لنوع الواجب
  static String getHomeworkTypeText(String type) {
    return homeworkTypes[type.toLowerCase()] ?? type;
  }

  /// الحصول على قائمة أنواع العلاج المتاحة
  static List<String> getAvailableTreatmentTypes() {
    return treatmentTypes.keys.toList();
  }

  /// الحصول على قائمة أنواع الفحوصات المتاحة
  static List<String> getAvailableExaminationTypes() {
    return examinationTypes.keys.toList();
  }

  /// التحقق من صحة نوع العلاج
  static bool isValidTreatmentType(String type) {
    return treatmentTypes.containsKey(type.toLowerCase());
  }

  /// التحقق من صحة نوع الفحص
  static bool isValidExaminationType(String type) {
    return examinationTypes.containsKey(type.toLowerCase());
  }
}
