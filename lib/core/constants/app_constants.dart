/// ثوابت التطبيق
class AppConstants {
  AppConstants._();

  // معلومات التطبيق
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';

  // Supabase Configuration
  static const String supabaseUrl = 'https://xqvdkdjnrcytswvfrkog.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhxdmRrZGpucmN5dHN3dmZya29nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyMTI5MzAsImV4cCI6MjA2ODc4ODkzMH0.zaHSYMMK1QIRwCckgZXhT287rdW2IQUbY5Ag4U7PiRg';

  // OpenAI Configuration
  static const String openAiApiKey = '********************************************************************************************************************************************************************';
  static const String openAiModel = 'gpt-4o-mini';
  static const String openAiBaseUrl = 'https://api.openai.com/v1';

  // API Endpoints
  static const String baseUrl = 'https://xqvdkdjnrcytswvfrkog.supabase.co/rest/v1';

  // Database Tables - Updated for Health app
  static const String patientsTable = 'patients';
  static const String appointmentsTable = 'appointments';
  static const String productsTable = 'products';
  static const String categoriesTable = 'categories';
  static const String weeklyResultsTable = 'weekly_results';
  static const String labTestsTable = 'lab_tests';
  static const String medicalRecordsTable = 'medical_records';
  static const String remindersTable = 'reminders';
  static const String timeSlotsTable = 'time_slots';
  static const String holidaysTable = 'holidays';
  static const String clinicInfoTable = 'clinic_info';
  static const String medicalInfoTable = 'medical_info';
  static const String adminsTable = 'admins';
  static const String diagnosisTable = 'diagnosis_reports';
  static const String assignmentsTable = 'assignments';
  static const String purchasesTable = 'purchases';

  // Shared Preferences Keys
  static const String keyIsLoggedIn = 'is_logged_in';
  static const String keyUserId = 'user_id';
  static const String keyUserEmail = 'user_email';
  static const String keyUserName = 'user_name';
  static const String keyUserPhone = 'user_phone';
  static const String keyThemeMode = 'theme_mode';
  static const String keyLanguage = 'language';
  static const String keyFirstTime = 'first_time';
  static const String keyNotificationsEnabled = 'notifications_enabled';
  static const String keyReminderSettings = 'reminder_settings';

  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 400);
  static const Duration longAnimationDuration = Duration(milliseconds: 600);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;

  static const double defaultRadius = 12.0;
  static const double smallRadius = 8.0;
  static const double largeRadius = 16.0;
  static const double extraLargeRadius = 24.0;

  static const double defaultElevation = 4.0;
  static const double smallElevation = 2.0;
  static const double largeElevation = 8.0;

  // Screen Breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  static const double desktopBreakpoint = 1440;

  // Image Sizes
  static const double smallImageSize = 40.0;
  static const double mediumImageSize = 80.0;
  static const double largeImageSize = 120.0;
  static const double extraLargeImageSize = 200.0;

  // List Item Heights
  static const double listItemHeight = 72.0;
  static const double smallListItemHeight = 56.0;
  static const double largeListItemHeight = 88.0;

  // Button Heights
  static const double buttonHeight = 48.0;
  static const double smallButtonHeight = 36.0;
  static const double largeButtonHeight = 56.0;

  // Input Field Heights
  static const double inputFieldHeight = 48.0;
  static const double smallInputFieldHeight = 40.0;
  static const double largeInputFieldHeight = 56.0;

  // App Bar Heights
  static const double appBarHeight = 56.0;
  static const double largeAppBarHeight = 72.0;

  // Bottom Navigation Bar Height
  static const double bottomNavBarHeight = 60.0;

  // Tab Bar Height
  static const double tabBarHeight = 48.0;

  // Card Heights
  static const double smallCardHeight = 120.0;
  static const double mediumCardHeight = 200.0;
  static const double largeCardHeight = 280.0;

  // Grid Constants
  static const int gridCrossAxisCount = 2;
  static const double gridChildAspectRatio = 0.8;
  static const double gridSpacing = 16.0;

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Cache Duration
  static const Duration cacheExpiration = Duration(hours: 1);
  static const Duration longCacheExpiration = Duration(days: 1);

  // Network Timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);

  // Retry Configuration
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  // Validation Constants
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int maxNotesLength = 500;

  // File Upload
  static const int maxFileSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx'];

  // Notification IDs
  static const int appointmentReminderNotificationId = 1000;
  static const int mealReminderNotificationId = 2000;
  static const int waterReminderNotificationId = 3000;
  static const int exerciseReminderNotificationId = 4000;
  static const int medicationReminderNotificationId = 5000;

  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayTimeFormat = 'hh:mm a';
  static const String displayDateTimeFormat = 'dd/MM/yyyy hh:mm a';

  // Regular Expressions
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phoneRegex = r'^[0-9]{10,15}$';
  static const String nameRegex = r'^[a-zA-Zأ-ي\s]+$';

  // Error Messages
  static const String genericErrorMessage = 'حدث خطأ غير متوقع';
  static const String networkErrorMessage = 'تحقق من اتصالك بالإنترنت';
  static const String serverErrorMessage = 'خطأ في الخادم، حاول مرة أخرى';
  static const String timeoutErrorMessage = 'انتهت مهلة الاتصال';

  // Success Messages
  static const String genericSuccessMessage = 'تمت العملية بنجاح';

  // Loading Messages
  static const String loadingMessage = 'جاري التحميل...';
  static const String savingMessage = 'جاري الحفظ...';
  static const String deletingMessage = 'جاري الحذف...';
  static const String updatingMessage = 'جاري التحديث...';

  // Empty State Messages
  static const String noDataMessage = 'لا توجد بيانات للعرض';
  static const String noResultsMessage = 'لا توجد نتائج مطابقة';
  static const String noAppointmentsMessage = 'لا توجد مواعيد';
  static const String noProductsMessage = 'لا توجد منتجات';

  // Feature Flags
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  static const bool enablePushNotifications = true;
  static const bool enableBiometricAuth = false;
  static const bool enableDarkMode = true;
}