import 'package:equatable/equatable.dart';

/// الفشل الأساسي
abstract class Failure extends Equatable {
  final String message;
  final int? code;

  const Failure({
    required this.message,
    this.code,
  });

  @override
  List<Object?> get props => [message, code];
}

/// فشل الخادم
class ServerFailure extends Failure {
  const ServerFailure({
    required super.message,
    super.code,
  });
}

/// فشل الشبكة
class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code,
  });
}

/// فشل التخزين المحلي
class CacheFailure extends Failure {
  const CacheFailure({
    required super.message,
    super.code,
  });
}

/// فشل التحقق من صحة البيانات
class ValidationFailure extends Failure {
  const ValidationFailure({
    required super.message,
    super.code,
  });
}

/// فشل المصادقة
class AuthFailure extends Failure {
  const AuthFailure({
    required super.message,
    super.code,
  });
}

/// فشل الصلاحيات
class PermissionFailure extends Failure {
  const PermissionFailure({
    required super.message,
    super.code,
  });
}

/// فشل غير معروف
class UnknownFailure extends Failure {
  const UnknownFailure({
    required super.message,
    super.code,
  });
}

/// فشل انتهاء المهلة الزمنية
class TimeoutFailure extends Failure {
  const TimeoutFailure({
    required super.message,
    super.code,
  });
}

/// فشل عدم العثور على البيانات
class NotFoundFailure extends Failure {
  const NotFoundFailure({
    required super.message,
    super.code,
  });
}

/// فشل تحليل البيانات
class ParsingFailure extends Failure {
  const ParsingFailure({
    required super.message,
    super.code,
  });
}

/// فشل الملف
class FileFailure extends Failure {
  const FileFailure({
    required super.message,
    super.code,
  });
}
