/// الاستثناء الأساسي
abstract class AppException implements Exception {
  final String message;
  final int? code;

  const AppException({
    required this.message,
    this.code,
  });

  @override
  String toString() => 'AppException: $message (Code: $code)';
}

/// استثناء الخادم
class ServerException extends AppException {
  const ServerException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'ServerException: $message (Code: $code)';
}

/// استثناء الشبكة
class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'NetworkException: $message (Code: $code)';
}

/// استثناء التخزين المحلي
class CacheException extends AppException {
  const CacheException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'CacheException: $message (Code: $code)';
}

/// استثناء التحقق من صحة البيانات
class ValidationException extends AppException {
  const ValidationException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'ValidationException: $message (Code: $code)';
}

/// استثناء المصادقة
class AuthException extends AppException {
  const AuthException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'AuthException: $message (Code: $code)';
}

/// استثناء الصلاحيات
class PermissionException extends AppException {
  const PermissionException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'PermissionException: $message (Code: $code)';
}

/// استثناء انتهاء المهلة الزمنية
class TimeoutException extends AppException {
  const TimeoutException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'TimeoutException: $message (Code: $code)';
}

/// استثناء عدم العثور على البيانات
class NotFoundException extends AppException {
  const NotFoundException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'NotFoundException: $message (Code: $code)';
}

/// استثناء تحليل البيانات
class ParsingException extends AppException {
  const ParsingException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'ParsingException: $message (Code: $code)';
}

/// استثناء الملف
class FileException extends AppException {
  const FileException({
    required super.message,
    super.code,
  });

  @override
  String toString() => 'FileException: $message (Code: $code)';
}
