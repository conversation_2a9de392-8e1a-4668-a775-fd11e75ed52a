import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';
import 'notification_service.dart';
import 'fcm_integration_service.dart';

/// خدمة استقبال الإشعارات للمريض من العيادة
class PatientNotificationReceiver {
  static final PatientNotificationReceiver _instance = PatientNotificationReceiver._internal();
  factory PatientNotificationReceiver() => _instance;
  PatientNotificationReceiver._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final NotificationService _notificationService = NotificationService();
  final FCMIntegrationService _fcmService = FCMIntegrationService();

  bool _isInitialized = false;

  /// تهيئة خدمة استقبال الإشعارات
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info(
        '🔔 Initializing patient notification receiver',
        category: LogCategory.notification,
      );

      // تهيئة FCM
      await _fcmService.initialize();

      // إعداد معالجات الرسائل المخصصة للمريض
      _setupPatientMessageHandlers();

      // الاشتراك في المواضيع الخاصة بالمريض
      await _subscribeToPatientTopics();

      _isInitialized = true;

      AppLogger.info(
        '✅ Patient notification receiver initialized',
        category: LogCategory.notification,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to initialize patient notification receiver',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// إعداد معالجات الرسائل المخصصة للمريض
  void _setupPatientMessageHandlers() {
    // معالج الرسائل في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // معالج فتح التطبيق من الإشعار
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // معالج الرسالة الأولية
    _handleInitialMessage();
  }

  /// معالج الرسائل في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    try {
      AppLogger.info(
        '📱 Patient foreground message received',
        category: LogCategory.notification,
        data: {
          'messageId': message.messageId ?? 'No ID',
          'title': message.notification?.title ?? 'No title',
          'body': message.notification?.body ?? 'No body',
          'data': message.data.toString(),
        },
      );

      // معالجة الرسالة حسب النوع
      await _processNotificationMessage(message);

      // عرض إشعار محلي
      if (message.notification != null) {
        await _notificationService.showNotification(
          id: message.hashCode,
          title: message.notification!.title ?? 'إشعار جديد',
          body: message.notification!.body ?? '',
          payload: _createPayloadFromMessage(message),
        );
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error handling foreground message',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// معالج فتح التطبيق من الإشعار
  Future<void> _handleMessageOpenedApp(RemoteMessage message) async {
    try {
      AppLogger.info(
        '👆 Patient message opened app',
        category: LogCategory.notification,
        data: {
          'messageId': message.messageId ?? 'No ID',
          'data': message.data.toString(),
        },
      );

      // معالجة التنقل بناءً على نوع الإشعار
      await _handleNotificationNavigation(message);
    } catch (e) {
      AppLogger.error(
        '❌ Error handling message opened app',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// معالج الرسالة الأولية
  Future<void> _handleInitialMessage() async {
    try {
      final initialMessage = await FirebaseMessaging.instance.getInitialMessage();

      if (initialMessage != null) {
        AppLogger.info(
          '🚀 Patient initial message found',
          category: LogCategory.notification,
          data: {
            'messageId': initialMessage.messageId ?? 'No ID',
            'data': initialMessage.data.toString(),
          },
        );

        await _handleNotificationNavigation(initialMessage);
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error handling initial message',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// معالجة رسالة الإشعار حسب النوع
  Future<void> _processNotificationMessage(RemoteMessage message) async {
    final data = message.data;
    final type = data['type'] ?? '';

    switch (type) {
      case 'appointment_reminder':
        await _handleAppointmentReminder(data);
        break;
      case 'appointment_confirmed':
        await _handleAppointmentConfirmed(data);
        break;
      case 'appointment_cancelled_by_clinic':
        await _handleAppointmentCancelledByClinic(data);
        break;
      case 'appointment_rescheduled_by_clinic':
        await _handleAppointmentRescheduledByClinic(data);
        break;
      case 'new_homework':
        await _handleNewHomework(data);
        break;
      case 'homework_feedback':
        await _handleHomeworkFeedback(data);
        break;
      case 'examination_result':
        await _handleExaminationResult(data);
        break;
      case 'general_announcement':
        await _handleGeneralAnnouncement(data);
        break;
      default:
        AppLogger.info(
          '📝 Unknown notification type received',
          category: LogCategory.notification,
          data: {'type': type},
        );
    }

    // حفظ الإشعار في قاعدة البيانات المحلية
    await _saveReceivedNotification(message);
  }

  /// معالجة تذكير الموعد
  Future<void> _handleAppointmentReminder(Map<String, dynamic> data) async {
    AppLogger.info(
      '📅 Processing appointment reminder',
      category: LogCategory.notification,
      data: data,
    );

    // يمكن إضافة منطق إضافي هنا مثل تحديث حالة الموعد
  }

  /// معالجة تأكيد الموعد من العيادة
  Future<void> _handleAppointmentConfirmed(Map<String, dynamic> data) async {
    AppLogger.info(
      '✅ Processing appointment confirmation',
      category: LogCategory.notification,
      data: data,
    );

    // تحديث حالة الموعد في قاعدة البيانات المحلية
    final appointmentId = data['appointmentId'];
    if (appointmentId != null) {
      try {
        await _supabase
            .from('appointments')
            .update({'status': 'confirmed'})
            .eq('id', appointmentId);
      } catch (e) {
        AppLogger.error(
          '❌ Failed to update appointment status',
          category: LogCategory.notification,
          error: e,
        );
      }
    }
  }

  /// معالجة إلغاء الموعد من العيادة
  Future<void> _handleAppointmentCancelledByClinic(Map<String, dynamic> data) async {
    AppLogger.info(
      '❌ Processing appointment cancellation by clinic',
      category: LogCategory.notification,
      data: data,
    );

    final appointmentId = data['appointmentId'];
    if (appointmentId != null) {
      try {
        await _supabase
            .from('appointments')
            .update({
              'status': 'cancelled',
              'cancellation_reason': data['reason'] ?? 'تم الإلغاء من قبل العيادة',
            })
            .eq('id', appointmentId);
      } catch (e) {
        AppLogger.error(
          '❌ Failed to update cancelled appointment',
          category: LogCategory.notification,
          error: e,
        );
      }
    }
  }

  /// معالجة تعديل الموعد من العيادة
  Future<void> _handleAppointmentRescheduledByClinic(Map<String, dynamic> data) async {
    AppLogger.info(
      '🔄 Processing appointment reschedule by clinic',
      category: LogCategory.notification,
      data: data,
    );

    // يمكن إضافة منطق تحديث الموعد هنا
  }

  /// معالجة واجب جديد
  Future<void> _handleNewHomework(Map<String, dynamic> data) async {
    AppLogger.info(
      '📚 Processing new homework notification',
      category: LogCategory.notification,
      data: data,
    );

    // يمكن إضافة منطق تحديث قائمة الواجبات
  }

  /// معالجة تقييم الواجب
  Future<void> _handleHomeworkFeedback(Map<String, dynamic> data) async {
    AppLogger.info(
      '📝 Processing homework feedback',
      category: LogCategory.notification,
      data: data,
    );
  }

  /// معالجة نتيجة فحص
  Future<void> _handleExaminationResult(Map<String, dynamic> data) async {
    AppLogger.info(
      '🔬 Processing examination result',
      category: LogCategory.notification,
      data: data,
    );
  }

  /// معالجة إعلان عام
  Future<void> _handleGeneralAnnouncement(Map<String, dynamic> data) async {
    AppLogger.info(
      '📢 Processing general announcement',
      category: LogCategory.notification,
      data: data,
    );
  }

  /// معالجة التنقل بناءً على الإشعار
  Future<void> _handleNotificationNavigation(RemoteMessage message) async {
    final data = message.data;
    final type = data['type'] ?? '';

    switch (type) {
      case 'appointment_reminder':
      case 'appointment_confirmed':
      case 'appointment_cancelled_by_clinic':
      case 'appointment_rescheduled_by_clinic':
        // التنقل لصفحة المواعيد
        AppLogger.info(
          '📅 Navigate to appointments',
          category: LogCategory.navigation,
          data: {'appointmentId': data['appointmentId']},
        );
        break;

      case 'new_homework':
      case 'homework_feedback':
        // التنقل لصفحة الواجبات
        AppLogger.info(
          '📚 Navigate to homework',
          category: LogCategory.navigation,
          data: {'homeworkId': data['homeworkId']},
        );
        break;

      case 'examination_result':
        // التنقل لصفحة الفحوصات
        AppLogger.info(
          '🔬 Navigate to examinations',
          category: LogCategory.navigation,
          data: {'examinationId': data['examinationId']},
        );
        break;

      default:
        // التنقل للصفحة الرئيسية
        AppLogger.info(
          '🏠 Navigate to home',
          category: LogCategory.navigation,
        );
    }
  }

  /// إنشاء payload من الرسالة
  String _createPayloadFromMessage(RemoteMessage message) {
    final data = message.data;
    if (data.isNotEmpty) {
      return data.entries.map((e) => '${e.key}:${e.value}').join(',');
    }
    return '';
  }

  /// حفظ الإشعار المستلم في قاعدة البيانات
  Future<void> _saveReceivedNotification(RemoteMessage message) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return;

      await _supabase.from('patient_notifications').insert({
        'patient_id': currentUser.id,
        'title': message.notification?.title ?? 'إشعار',
        'body': message.notification?.body ?? '',
        'type': message.data['type'] ?? 'general',
        'data': message.data,
        'message_id': message.messageId,
        'received_at': DateTime.now().toIso8601String(),
        'is_read': false,
      });

      AppLogger.info(
        '💾 Received notification saved',
        category: LogCategory.notification,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to save received notification',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// الاشتراك في المواضيع الخاصة بالمريض
  Future<void> _subscribeToPatientTopics() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return;

      // الاشتراك في المواضيع العامة
      await _fcmService.subscribeToTopic('patient_notifications');
      await _fcmService.subscribeToTopic('general_announcements');
      
      // الاشتراك في موضوع خاص بالمريض
      await _fcmService.subscribeToTopic('patient_${currentUser.id}');

      AppLogger.info(
        '📢 Subscribed to patient topics',
        category: LogCategory.notification,
        data: {'patientId': currentUser.id},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to subscribe to patient topics',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// إلغاء الاشتراك من المواضيع عند تسجيل الخروج
  Future<void> unsubscribeFromPatientTopics() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return;

      await _fcmService.unsubscribeFromTopic('patient_notifications');
      await _fcmService.unsubscribeFromTopic('general_announcements');
      await _fcmService.unsubscribeFromTopic('patient_${currentUser.id}');

      AppLogger.info(
        '📢 Unsubscribed from patient topics',
        category: LogCategory.notification,
        data: {'patientId': currentUser.id},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to unsubscribe from patient topics',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// الحصول على الإشعارات غير المقروءة
  Future<List<Map<String, dynamic>>> getUnreadNotifications() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return [];

      final response = await _supabase
          .from('patient_notifications')
          .select()
          .eq('patient_id', currentUser.id)
          .eq('is_read', false)
          .order('received_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get unread notifications',
        category: LogCategory.notification,
        error: e,
      );
      return [];
    }
  }

  /// تحديد الإشعار كمقروء
  Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      await _supabase
          .from('patient_notifications')
          .update({'is_read': true})
          .eq('id', notificationId);

      AppLogger.info(
        '✅ Notification marked as read',
        category: LogCategory.notification,
        data: {'notificationId': notificationId},
      );

      return true;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to mark notification as read',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<bool> markAllNotificationsAsRead() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return false;

      await _supabase
          .from('patient_notifications')
          .update({'is_read': true})
          .eq('patient_id', currentUser.id)
          .eq('is_read', false);

      AppLogger.info(
        '✅ All notifications marked as read',
        category: LogCategory.notification,
      );

      return true;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to mark all notifications as read',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }
}