import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';
import 'simple_fcm_service.dart';
import 'fcm_token_service.dart';

/// مدير نظام الإشعارات المبسط - FCM v1 فقط
class SimpleNotificationManager {
  static final SimpleNotificationManager _instance =
      SimpleNotificationManager._internal();
  factory SimpleNotificationManager() => _instance;
  SimpleNotificationManager._internal();

  final SimpleFCMService _fcmService = SimpleFCMService();
  final FCMTokenService _tokenService = FCMTokenService();
  final SupabaseClient _supabase = Supabase.instance.client;

  bool _isInitialized = false;

  /// تهيئة نظام الإشعارات المبسط
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info(
        '🚀 Initializing simple notification system',
        category: LogCategory.notification,
      );

      // تهيئة خدمة FCM المبسطة فقط
      await _fcmService.initialize();

      // حفظ FCM Token
      await _saveFCMToken();

      _isInitialized = true;

      AppLogger.info(
        '✅ Simple notification system initialized successfully',
        category: LogCategory.notification,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to initialize notification system',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// حفظ FCM Token
  Future<void> _saveFCMToken() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      final fcmToken = _fcmService.fcmToken;

      if (currentUser != null && fcmToken != null) {
        await _tokenService.saveToken(currentUser.id, fcmToken);

        AppLogger.info(
          '💾 FCM token saved',
          category: LogCategory.notification,
          data: {'userId': currentUser.id},
        );
      }
    } catch (e) {
      AppLogger.error(
        '❌ Failed to save FCM token',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// معالجة تسجيل دخول المستخدم
  Future<void> onUserLogin(String userId) async {
    try {
      AppLogger.info(
        '👤 Handling user login for notifications',
        category: LogCategory.notification,
        data: {'userId': userId},
      );

      // حفظ التوكن للمستخدم الجديد
      await _saveFCMToken();

      // إعداد الاشتراكات
      await _fcmService.onUserLogin(userId);

      AppLogger.info(
        '✅ User login handled for notifications',
        category: LogCategory.notification,
        data: {'userId': userId},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to handle user login for notifications',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// معالجة تسجيل خروج المستخدم
  Future<void> onUserLogout() async {
    try {
      AppLogger.info(
        '👋 Handling user logout for notifications',
        category: LogCategory.notification,
      );

      // إلغاء الاشتراكات
      await _fcmService.onUserLogout();

      // حذف التوكن من قاعدة البيانات
      final currentUser = _supabase.auth.currentUser;
      if (currentUser != null) {
        await _tokenService.deleteToken(currentUser.id, _fcmService.fcmToken);
      }

      AppLogger.info(
        '✅ User logout handled for notifications',
        category: LogCategory.notification,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to handle user logout for notifications',
        category: LogCategory.notification,
        error: e,
      );
    }
  }

  /// الحصول على FCM Token الحالي
  String? get fcmToken => _fcmService.fcmToken;

  /// التحقق من حالة التهيئة
  bool get isInitialized => _isInitialized;

  /// التحقق من حالة تهيئة FCM
  bool get isFCMInitialized => _fcmService.isInitialized;
}
