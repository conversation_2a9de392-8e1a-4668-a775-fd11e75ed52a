import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';

/// خدمة Supabase مع تتبع مفصل للأخطاء
class SupabaseService {
  static final SupabaseClient _client = Supabase.instance.client;

  /// الحصول على client مع تتبع
  static SupabaseClient get client => _client;

  /// تسجيل الدخول مع تتبع
  static Future<AuthResponse> signInWithPassword({
    required String email,
    required String password,
  }) async {
    AppLogger.info('🔐 Attempting sign in',
      category: LogCategory.auth,
      data: {'email': email}
    );

    try {
      final stopwatch = Stopwatch()..start();

      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      stopwatch.stop();

      AppLogger.info('✅ Sign in successful',
        category: LogCategory.auth,
        data: {
          'email': email,
          'userId': response.user?.id ?? 'null',
          'duration': '${stopwatch.elapsedMilliseconds}ms',
          'hasSession': (response.session != null).toString(),
        }
      );

      return response;
    } on AuthException catch (e) {
      AppLogger.error('❌ Auth Exception during sign in',
        category: LogCategory.auth,
        data: {
          'email': email,
          'errorMessage': e.message,
          'statusCode': e.statusCode?.toString() ?? 'null',
        },
        error: e
      );
      rethrow;
    } catch (e) {
      AppLogger.error('❌ Unexpected error during sign in',
        category: LogCategory.auth,
        data: {
          'email': email,
          'errorType': e.runtimeType.toString(),
        },
        error: e
      );
      rethrow;
    }
  }

  /// التسجيل مع تتبع
  static Future<AuthResponse> signUp({
    required String email,
    required String password,
    Map<String, dynamic>? data,
  }) async {
    AppLogger.info('📝 Attempting sign up',
      category: LogCategory.auth,
      data: {
        'email': email,
        'hasUserData': (data != null).toString(),
        'userData': data?.toString() ?? 'null',
      }
    );

    try {
      final stopwatch = Stopwatch()..start();

      final response = await _client.auth.signUp(
        email: email,
        password: password,
        data: data,
      );

      stopwatch.stop();

      AppLogger.info('✅ Sign up successful',
        category: LogCategory.auth,
        data: {
          'email': email,
          'userId': response.user?.id ?? 'null',
          'duration': '${stopwatch.elapsedMilliseconds}ms',
          'hasSession': (response.session != null).toString(),
          'emailConfirmed': (response.user?.emailConfirmedAt != null).toString(),
        }
      );

      return response;
    } on AuthException catch (e) {
      AppLogger.error('❌ Auth Exception during sign up',
        category: LogCategory.auth,
        data: {
          'email': email,
          'errorMessage': e.message,
          'statusCode': e.statusCode?.toString() ?? 'null',
        },
        error: e
      );
      rethrow;
    } catch (e) {
      AppLogger.error('❌ Unexpected error during sign up',
        category: LogCategory.auth,
        data: {
          'email': email,
          'errorType': e.runtimeType.toString(),
        },
        error: e
      );
      rethrow;
    }
  }

  /// إدراج بيانات مع تتبع
  static Future<List<Map<String, dynamic>>> insert({
    required String table,
    required Map<String, dynamic> data,
    bool returnData = true,
  }) async {
    AppLogger.info('📝 Inserting data',
      category: LogCategory.database,
      data: {
        'table': table,
        'dataKeys': data.keys.toList().toString(),
        'returnData': returnData.toString(),
      }
    );

    try {
      final stopwatch = Stopwatch()..start();

      final response = returnData
          ? await _client.from(table).insert(data).select()
          : await _client.from(table).insert(data);

      stopwatch.stop();

      AppLogger.info('✅ Data inserted successfully',
        category: LogCategory.database,
        data: {
          'table': table,
          'duration': '${stopwatch.elapsedMilliseconds}ms',
          'recordsAffected': response is List ? response.length.toString() : '1',
        }
      );

      return response is List ? List<Map<String, dynamic>>.from(response) : [response];
    } on PostgrestException catch (e) {
      AppLogger.error('❌ Postgrest Exception during insert',
        category: LogCategory.database,
        data: {
          'table': table,
          'errorMessage': e.message,
          'errorCode': e.code ?? 'null',
          'details': e.details ?? 'null',
          'hint': e.hint ?? 'null',
        },
        error: e
      );
      rethrow;
    } catch (e) {
      AppLogger.error('❌ Unexpected error during insert',
        category: LogCategory.database,
        data: {
          'table': table,
          'errorType': e.runtimeType.toString(),
        },
        error: e
      );
      rethrow;
    }
  }

  /// استعلام بيانات مع تتبع
  static Future<List<Map<String, dynamic>>> select({
    required String table,
    String columns = '*',
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
  }) async {
    AppLogger.info('🔍 Selecting data',
      category: LogCategory.database,
      data: {
        'table': table,
        'columns': columns,
        'hasFilters': (filters != null).toString(),
        'orderBy': orderBy ?? 'null',
        'limit': limit?.toString() ?? 'null',
      }
    );

    try {
      final stopwatch = Stopwatch()..start();

      // بناء الاستعلام بطريقة متسلسلة
      dynamic query = _client.from(table).select(columns);

      // إضافة الفلاتر
      if (filters != null) {
        filters.forEach((key, value) {
          query = query.eq(key, value);
        });
      }

      // إضافة الترتيب
      if (orderBy != null) {
        query = query.order(orderBy, ascending: ascending);
      }

      // إضافة الحد الأقصى
      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;

      stopwatch.stop();

      AppLogger.info('✅ Data selected successfully',
        category: LogCategory.database,
        data: {
          'table': table,
          'duration': '${stopwatch.elapsedMilliseconds}ms',
          'recordsFound': response.length.toString(),
        }
      );

      return List<Map<String, dynamic>>.from(response);
    } on PostgrestException catch (e) {
      AppLogger.error('❌ Postgrest Exception during select',
        category: LogCategory.database,
        data: {
          'table': table,
          'errorMessage': e.message,
          'errorCode': e.code ?? 'null',
          'details': e.details ?? 'null',
        },
        error: e
      );
      rethrow;
    } catch (e) {
      AppLogger.error('❌ Unexpected error during select',
        category: LogCategory.database,
        data: {
          'table': table,
          'errorType': e.runtimeType.toString(),
        },
        error: e
      );
      rethrow;
    }
  }

  /// تحديث بيانات مع تتبع
  static Future<List<Map<String, dynamic>>> update({
    required String table,
    required Map<String, dynamic> data,
    required Map<String, dynamic> filters,
    bool returnData = true,
  }) async {
    AppLogger.info('✏️ Updating data',
      category: LogCategory.database,
      data: {
        'table': table,
        'dataKeys': data.keys.toList().toString(),
        'filterKeys': filters.keys.toList().toString(),
      }
    );

    try {
      final stopwatch = Stopwatch()..start();

      // بناء الاستعلام
      dynamic query = _client.from(table).update(data);

      // إضافة الفلاتر
      filters.forEach((key, value) {
        query = query.eq(key, value);
      });

      // إضافة select إذا كان مطلوب
      if (returnData) {
        query = query.select();
      }

      final response = await query;

      stopwatch.stop();

      AppLogger.info('✅ Data updated successfully',
        category: LogCategory.database,
        data: {
          'table': table,
          'duration': '${stopwatch.elapsedMilliseconds}ms',
          'recordsAffected': response is List ? response.length.toString() : '1',
        }
      );

      return response is List ? List<Map<String, dynamic>>.from(response) : [response];
    } on PostgrestException catch (e) {
      AppLogger.error('❌ Postgrest Exception during update',
        category: LogCategory.database,
        data: {
          'table': table,
          'errorMessage': e.message,
          'errorCode': e.code ?? 'null',
          'details': e.details ?? 'null',
        },
        error: e
      );
      rethrow;
    } catch (e) {
      AppLogger.error('❌ Unexpected error during update',
        category: LogCategory.database,
        data: {
          'table': table,
          'errorType': e.runtimeType.toString(),
        },
        error: e
      );
      rethrow;
    }
  }

  /// حذف بيانات مع تتبع
  static Future<void> delete({
    required String table,
    required Map<String, dynamic> filters,
  }) async {
    AppLogger.info('🗑️ Deleting data',
      category: LogCategory.database,
      data: {
        'table': table,
        'filterKeys': filters.keys.toList().toString(),
      }
    );

    try {
      final stopwatch = Stopwatch()..start();

      var query = _client.from(table).delete();

      // إضافة الفلاتر
      filters.forEach((key, value) {
        query = query.eq(key, value);
      });

      await query;

      stopwatch.stop();

      AppLogger.info('✅ Data deleted successfully',
        category: LogCategory.database,
        data: {
          'table': table,
          'duration': '${stopwatch.elapsedMilliseconds}ms',
        }
      );
    } on PostgrestException catch (e) {
      AppLogger.error('❌ Postgrest Exception during delete',
        category: LogCategory.database,
        data: {
          'table': table,
          'errorMessage': e.message,
          'errorCode': e.code ?? 'null',
          'details': e.details ?? 'null',
        },
        error: e
      );
      rethrow;
    } catch (e) {
      AppLogger.error('❌ Unexpected error during delete',
        category: LogCategory.database,
        data: {
          'table': table,
          'errorType': e.runtimeType.toString(),
        },
        error: e
      );
      rethrow;
    }
  }
}
