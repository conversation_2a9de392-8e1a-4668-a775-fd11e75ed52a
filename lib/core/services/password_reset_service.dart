import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';
import 'shared_preferences_service.dart';

/// خدمة إعادة تعيين كلمة المرور
class PasswordResetService {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static final SharedPreferencesService _prefs = SharedPreferencesService();

  /// إرسال كود إعادة تعيين كلمة المرور
  static Future<PasswordResetResult> sendResetCode({
    required String email,
  }) async {
    try {
      AppLogger.info(
        '📧 Sending password reset code',
        category: LogCategory.auth,
        data: {'email': email},
      );

      // التحقق من صحة البريد الإلكتروني
      if (!_isValidEmail(email)) {
        return PasswordResetResult.error('البريد الإلكتروني غير صحيح');
      }

      // التحقق من وجود المستخدم
      final userExists = await _checkUserExists(email);
      if (!userExists) {
        return PasswordResetResult.error('لا يوجد حساب مرتبط بهذا البريد الإلكتروني');
      }

      // حفظ البريد الإلكتروني مؤقتاً
      await _prefs.setString('reset_email', email);

      // إرسال كود إعادة التعيين
      await _supabase.auth.resetPasswordForEmail(
        email,
        redirectTo: null, // لا نريد redirect
      );

      // حفظ وقت إرسال الكود
      await _prefs.setString('reset_sent_time', DateTime.now().toIso8601String());

      AppLogger.info(
        '✅ Password reset code sent successfully',
        category: LogCategory.auth,
        data: {'email': email},
      );

      return PasswordResetResult.success(
        'تم إرسال كود إعادة تعيين كلمة المرور إلى بريدك الإلكتروني',
      );
    } on AuthException catch (e) {
      AppLogger.error(
        '❌ Auth error sending reset code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email, 'code': e.statusCode},
      );

      return PasswordResetResult.error(_getAuthErrorMessage(e));
    } catch (e) {
      AppLogger.error(
        '❌ Unexpected error sending reset code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email},
      );

      return PasswordResetResult.error('حدث خطأ غير متوقع. حاول مرة أخرى.');
    }
  }

  /// التحقق من كود إعادة التعيين وتغيير كلمة المرور
  static Future<PasswordResetResult> verifyCodeAndResetPassword({
    required String email,
    required String token,
    required String newPassword,
  }) async {
    try {
      AppLogger.info(
        '🔍 Verifying reset code and changing password',
        category: LogCategory.auth,
        data: {'email': email},
      );

      // التحقق من قوة كلمة المرور الجديدة
      final passwordValidation = _validatePassword(newPassword);
      if (!passwordValidation.isValid) {
        return PasswordResetResult.error(passwordValidation.message);
      }

      // التحقق من الكود وتغيير كلمة المرور
      final response = await _supabase.auth.verifyOTP(
        email: email,
        token: token,
        type: OtpType.recovery,
      );

      if (response.user != null) {
        // تغيير كلمة المرور
        await _supabase.auth.updateUser(
          UserAttributes(password: newPassword),
        );

        // مسح البيانات المؤقتة
        await _clearTemporaryData();

        AppLogger.info(
          '✅ Password reset successfully',
          category: LogCategory.auth,
          data: {'email': email, 'userId': response.user!.id},
        );

        return PasswordResetResult.success(
          'تم تغيير كلمة المرور بنجاح',
        );
      } else {
        return PasswordResetResult.error('فشل في التحقق من الكود');
      }
    } on AuthException catch (e) {
      AppLogger.error(
        '❌ Auth error verifying reset code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email, 'code': e.statusCode},
      );

      return PasswordResetResult.error(_getAuthErrorMessage(e));
    } catch (e) {
      AppLogger.error(
        '❌ Unexpected error verifying reset code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email},
      );

      return PasswordResetResult.error('حدث خطأ غير متوقع. حاول مرة أخرى.');
    }
  }

  /// إعادة إرسال كود إعادة التعيين
  static Future<PasswordResetResult> resendResetCode(String email) async {
    try {
      // التحقق من الوقت المسموح لإعادة الإرسال
      final canResend = await _canResendCode();
      if (!canResend) {
        final remainingTime = await _getRemainingTime();
        return PasswordResetResult.error(
          'يمكنك إعادة إرسال الكود بعد $remainingTime ثانية',
        );
      }

      AppLogger.info(
        '🔄 Resending password reset code',
        category: LogCategory.auth,
        data: {'email': email},
      );

      // إعادة إرسال الكود
      await _supabase.auth.resetPasswordForEmail(email);

      // تحديث وقت الإرسال
      await _prefs.setString('reset_sent_time', DateTime.now().toIso8601String());

      AppLogger.info(
        '✅ Password reset code resent successfully',
        category: LogCategory.auth,
        data: {'email': email},
      );

      return PasswordResetResult.success('تم إعادة إرسال كود إعادة التعيين');
    } on AuthException catch (e) {
      AppLogger.error(
        '❌ Auth error resending reset code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email},
      );

      return PasswordResetResult.error(_getAuthErrorMessage(e));
    } catch (e) {
      AppLogger.error(
        '❌ Unexpected error resending reset code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email},
      );

      return PasswordResetResult.error('حدث خطأ غير متوقع. حاول مرة أخرى.');
    }
  }

  /// التحقق من وجود المستخدم
  static Future<bool> _checkUserExists(String email) async {
    try {
      final response = await _supabase
          .from('patients')
          .select('id')
          .eq('email', email)
          .maybeSingle();
      
      return response != null;
    } catch (e) {
      AppLogger.error(
        '❌ Error checking user existence',
        category: LogCategory.auth,
        error: e,
        data: {'email': email},
      );
      return false;
    }
  }

  /// التحقق من إمكانية إعادة الإرسال
  static Future<bool> _canResendCode() async {
    final sentTimeStr = _prefs.getString('reset_sent_time');
    if (sentTimeStr == null) return true;

    final sentTime = DateTime.parse(sentTimeStr);
    final now = DateTime.now();
    final difference = now.difference(sentTime).inSeconds;

    return difference >= 60; // دقيقة واحدة
  }

  /// الحصول على الوقت المتبقي لإعادة الإرسال
  static Future<int> _getRemainingTime() async {
    final sentTimeStr = _prefs.getString('reset_sent_time');
    if (sentTimeStr == null) return 0;

    final sentTime = DateTime.parse(sentTimeStr);
    final now = DateTime.now();
    final difference = now.difference(sentTime).inSeconds;

    return 60 - difference;
  }

  /// مسح البيانات المؤقتة
  static Future<void> _clearTemporaryData() async {
    await _prefs.remove('reset_email');
    await _prefs.remove('reset_sent_time');
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// التحقق من قوة كلمة المرور
  static PasswordValidation _validatePassword(String password) {
    if (password.length < 8) {
      return PasswordValidation(
        false,
        'كلمة المرور يجب أن تكون 8 خانات على الأقل',
      );
    }
    return PasswordValidation(true, '');
  }

  /// الحصول على رسالة خطأ مناسبة
  static String _getAuthErrorMessage(AuthException e) {
    switch (e.message.toLowerCase()) {
      case 'user not found':
        return 'لا يوجد حساب مرتبط بهذا البريد الإلكتروني';
      case 'invalid email':
        return 'البريد الإلكتروني غير صحيح';
      case 'invalid verification code':
      case 'token has expired':
        return 'كود التحقق غير صحيح أو منتهي الصلاحية';
      case 'too many requests':
        return 'تم إرسال طلبات كثيرة. حاول بعد قليل';
      default:
        return e.message;
    }
  }
}

/// نتيجة عملية إعادة تعيين كلمة المرور
class PasswordResetResult {
  final bool isSuccess;
  final String message;

  PasswordResetResult._(this.isSuccess, this.message);

  factory PasswordResetResult.success(String message) {
    return PasswordResetResult._(true, message);
  }

  factory PasswordResetResult.error(String message) {
    return PasswordResetResult._(false, message);
  }
}

/// نتيجة التحقق من كلمة المرور
class PasswordValidation {
  final bool isValid;
  final String message;

  PasswordValidation(this.isValid, this.message);
}
