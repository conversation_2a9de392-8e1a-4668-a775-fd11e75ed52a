import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';
import 'notification_service.dart';
import 'fcm_token_service.dart';

/// معالج الرسائل في الخلفية
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  AppLogger.info(
    '🔔 Background message received',
    category: LogCategory.general,
    data: {
      'messageId': message.messageId ?? 'No ID',
      'title': message.notification?.title ?? 'No title',
      'body': message.notification?.body ?? 'No body',
    },
  );
}

/// خدمة تكامل Firebase Cloud Messaging
class FCMIntegrationService {
  static final FCMIntegrationService _instance =
      FCMIntegrationService._internal();
  factory FCMIntegrationService() => _instance;
  FCMIntegrationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final NotificationService _notificationService = NotificationService();
  final SupabaseClient _supabase = Supabase.instance.client;

  String? _fcmToken;
  bool _isInitialized = false;

  /// الحصول على FCM Token
  String? get fcmToken => _fcmToken;

  /// تهيئة خدمة FCM
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info(
        '🚀 Initializing FCM service',
        category: LogCategory.general,
      );

      // طلب الأذونات
      await _requestPermissions();

      // الحصول على FCM Token
      await _getFCMToken();

      // إعداد معالجات الرسائل
      _setupMessageHandlers();

      // تهيئة خدمة الإشعارات المحلية
      await _notificationService.initialize();

      _isInitialized = true;

      AppLogger.info(
        '✅ FCM service initialized successfully',
        category: LogCategory.general,
        data: {'fcmToken': _fcmToken ?? 'No token'},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to initialize FCM service',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// طلب أذونات الإشعارات
  Future<void> _requestPermissions() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      AppLogger.info(
        '🔐 FCM permissions requested',
        category: LogCategory.general,
        data: {'status': settings.authorizationStatus.toString()},
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        AppLogger.info(
          '✅ FCM permissions granted',
          category: LogCategory.general,
        );
      } else {
        AppLogger.warning(
          '⚠️ FCM permissions denied',
          category: LogCategory.general,
        );
      }
    } catch (e) {
      AppLogger.error(
        '❌ Failed to request FCM permissions',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// الحصول على FCM Token
  Future<void> _getFCMToken() async {
    try {
      _fcmToken = await _firebaseMessaging.getToken();

      AppLogger.info(
        '🔑 FCM token obtained',
        category: LogCategory.general,
        data: {'token': _fcmToken ?? 'No token'},
      );

      // مراقبة تحديث التوكن
      _firebaseMessaging.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        AppLogger.info(
          '🔄 FCM token refreshed',
          category: LogCategory.general,
          data: {'newToken': newToken},
        );

        // يمكن إرسال التوكن الجديد للخادم هنا
        _sendTokenToServer(newToken);
      });
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get FCM token',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// إعداد معالجات الرسائل
  void _setupMessageHandlers() {
    // معالج الرسائل عندما يكون التطبيق في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // معالج النقر على الإشعار عندما يكون التطبيق في الخلفية
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // معالج النقر على الإشعار عندما يكون التطبيق مغلق
    _handleInitialMessage();
  }

  /// معالج الرسائل في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    AppLogger.info(
      '📱 Foreground message received',
      category: LogCategory.general,
      data: {
        'messageId': message.messageId ?? 'No ID',
        'title': message.notification?.title ?? 'No title',
        'body': message.notification?.body ?? 'No body',
        'data': message.data.toString(),
      },
    );

    // عرض إشعار محلي
    if (message.notification != null) {
      await _notificationService.showNotification(
        id: message.hashCode,
        title: message.notification!.title ?? 'إشعار جديد',
        body: message.notification!.body ?? '',
        payload: _createPayloadFromMessage(message),
      );
    }
  }

  /// معالج فتح التطبيق من الإشعار
  Future<void> _handleMessageOpenedApp(RemoteMessage message) async {
    AppLogger.info(
      '👆 Message opened app',
      category: LogCategory.general,
      data: {
        'messageId': message.messageId ?? 'No ID',
        'data': message.data.toString(),
      },
    );

    // معالجة التنقل بناءً على نوع الإشعار
    _handleNotificationNavigation(message);
  }

  /// معالج الرسالة الأولية (عند فتح التطبيق من إشعار)
  Future<void> _handleInitialMessage() async {
    final initialMessage = await _firebaseMessaging.getInitialMessage();

    if (initialMessage != null) {
      AppLogger.info(
        '🚀 Initial message found',
        category: LogCategory.general,
        data: {
          'messageId': initialMessage.messageId ?? 'No ID',
          'data': initialMessage.data.toString(),
        },
      );

      _handleNotificationNavigation(initialMessage);
    }
  }

  /// معالجة التنقل بناءً على الإشعار
  void _handleNotificationNavigation(RemoteMessage message) {
    final data = message.data;

    if (data.containsKey('type')) {
      switch (data['type']) {
        case 'appointment':
          AppLogger.info(
            '📅 Navigate to appointment',
            category: LogCategory.navigation,
            data: {'appointmentId': data['appointmentId']},
          );
          // التنقل لصفحة المواعيد
          break;

        case 'homework':
          AppLogger.info(
            '📚 Navigate to homework',
            category: LogCategory.navigation,
            data: {'homeworkId': data['homeworkId']},
          );
          // التنقل لصفحة الواجبات
          break;

        case 'examination':
          AppLogger.info(
            '🔬 Navigate to examination',
            category: LogCategory.navigation,
            data: {'examinationId': data['examinationId']},
          );
          // التنقل لصفحة الفحوصات
          break;

        default:
          AppLogger.info(
            '🏠 Navigate to home',
            category: LogCategory.navigation,
          );
        // التنقل للصفحة الرئيسية
      }
    }
  }

  /// إنشاء payload من الرسالة
  String _createPayloadFromMessage(RemoteMessage message) {
    final data = message.data;
    if (data.isNotEmpty) {
      return data.entries.map((e) => '${e.key}:${e.value}').join(',');
    }
    return '';
  }

  /// إرسال التوكن للخادم
  Future<void> _sendTokenToServer(String token) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser != null) {
        final fcmTokenService = FCMTokenService();
        await fcmTokenService.saveToken(currentUser.id, token);

        AppLogger.info(
          '📤 Token sent to server successfully',
          category: LogCategory.api,
          data: {'userId': currentUser.id},
        );
      } else {
        AppLogger.warning(
          '⚠️ No authenticated user to save token for',
          category: LogCategory.api,
        );
      }
    } catch (e) {
      AppLogger.error(
        '❌ Failed to send token to server',
        category: LogCategory.api,
        error: e,
      );
    }
  }

  /// الاشتراك في موضوع
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);

      AppLogger.info(
        '📢 Subscribed to topic',
        category: LogCategory.general,
        data: {'topic': topic},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to subscribe to topic',
        category: LogCategory.general,
        error: e,
        data: {'topic': topic},
      );
    }
  }

  /// إلغاء الاشتراك من موضوع
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);

      AppLogger.info(
        '📢 Unsubscribed from topic',
        category: LogCategory.general,
        data: {'topic': topic},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to unsubscribe from topic',
        category: LogCategory.general,
        error: e,
        data: {'topic': topic},
      );
    }
  }

  /// تحديث إعدادات الإشعارات
  Future<void> updateNotificationSettings({
    bool enableAppointmentReminders = true,
    bool enableHomeworkNotifications = true,
    bool enableExaminationResults = true,
  }) async {
    try {
      // الاشتراك أو إلغاء الاشتراك من المواضيع بناءً على الإعدادات
      if (enableAppointmentReminders) {
        await subscribeToTopic('appointment_reminders');
      } else {
        await unsubscribeFromTopic('appointment_reminders');
      }

      if (enableHomeworkNotifications) {
        await subscribeToTopic('homework_notifications');
      } else {
        await unsubscribeFromTopic('homework_notifications');
      }

      if (enableExaminationResults) {
        await subscribeToTopic('examination_results');
      } else {
        await unsubscribeFromTopic('examination_results');
      }

      AppLogger.info(
        '⚙️ Notification settings updated',
        category: LogCategory.general,
        data: {
          'appointments': enableAppointmentReminders.toString(),
          'homework': enableHomeworkNotifications.toString(),
          'examinations': enableExaminationResults.toString(),
        },
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to update notification settings',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// معالجة تسجيل دخول المستخدم
  Future<void> onUserLogin(String userId) async {
    try {
      AppLogger.info(
        '👤 User logged in, updating FCM',
        category: LogCategory.general,
        data: {'userId': userId},
      );

      // التأكد من الحصول على FCM Token أولاً
      if (_fcmToken == null) {
        await _getFCMToken();
      }

      // إرسال التوكن للخادم
      if (_fcmToken != null) {
        final fcmTokenService = FCMTokenService();
        await fcmTokenService.saveToken(userId, _fcmToken!);
        
        AppLogger.info(
          '📤 FCM Token saved for user',
          category: LogCategory.general,
          data: {'userId': userId},
        );
      } else {
        AppLogger.warning(
          '⚠️ No FCM token available to save',
          category: LogCategory.general,
          data: {'userId': userId},
        );
      }

      // الاشتراك في المواضيع الافتراضية
      await subscribeToTopic('user_$userId');
      await subscribeToTopic('appointment_reminders');
      await subscribeToTopic('homework_notifications');
      await subscribeToTopic('examination_results');
    } catch (e) {
      AppLogger.error(
        '❌ Failed to handle user login',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// معالجة تسجيل خروج المستخدم
  Future<void> onUserLogout() async {
    try {
      AppLogger.info(
        '👤 User logged out, cleaning up FCM',
        category: LogCategory.general,
      );

      // إلغاء الاشتراك من جميع المواضيع
      await unsubscribeFromTopic('appointment_reminders');
      await unsubscribeFromTopic('homework_notifications');
      await unsubscribeFromTopic('examination_results');
    } catch (e) {
      AppLogger.error(
        '❌ Failed to handle user logout',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// تنظيف الموارد
  void dispose() {
    AppLogger.info('🧹 FCM service disposed', category: LogCategory.general);
  }
}
