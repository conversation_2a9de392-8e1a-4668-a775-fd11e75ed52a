import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/app_constants.dart';
import '../constants/app_strings.dart';

/// فئة الأدوات المساعدة
class Helpers {
  Helpers._();

  /// تنسيق التاريخ للعرض
  static String formatDate(DateTime date) {
    return DateFormat(AppConstants.displayDateFormat, 'ar').format(date);
  }

  /// تنسيق الوقت للعرض
  static String formatTime(DateTime time) {
    return DateFormat(AppConstants.displayTimeFormat, 'ar').format(time);
  }

  /// تنسيق التاريخ والوقت للعرض
  static String formatDateTime(DateTime dateTime) {
    return DateFormat(
      AppConstants.displayDateTimeFormat,
      'ar',
    ).format(dateTime);
  }

  /// تنسيق التاريخ لقاعدة البيانات
  static String formatDateForDb(DateTime date) {
    return DateFormat(AppConstants.dateFormat).format(date);
  }

  /// تنسيق الوقت لقاعدة البيانات
  static String formatTimeForDb(DateTime time) {
    return DateFormat(AppConstants.timeFormat).format(time);
  }

  /// تنسيق التاريخ والوقت لقاعدة البيانات
  static String formatDateTimeForDb(DateTime dateTime) {
    return DateFormat(AppConstants.dateTimeFormat).format(dateTime);
  }

  /// تحويل النص إلى تاريخ
  static DateTime? parseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      return DateFormat(AppConstants.dateFormat).parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// تحويل النص إلى وقت
  static DateTime? parseTime(String? timeString) {
    if (timeString == null || timeString.isEmpty) return null;
    try {
      return DateFormat(AppConstants.timeFormat).parse(timeString);
    } catch (e) {
      return null;
    }
  }

  /// تحويل النص إلى تاريخ ووقت
  static DateTime? parseDateTime(String? dateTimeString) {
    if (dateTimeString == null || dateTimeString.isEmpty) return null;
    try {
      return DateFormat(AppConstants.dateTimeFormat).parse(dateTimeString);
    } catch (e) {
      return null;
    }
  }

  /// تنسيق السعر
  static String formatPrice(double price) {
    return '${price.toStringAsFixed(2)} د.أ';
  }

  /// تنسيق النسبة المئوية
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// تنسيق الوزن
  static String formatWeight(double weight) {
    return '${weight.toStringAsFixed(1)} كجم';
  }

  /// تنسيق الطول
  static String formatHeight(double height) {
    return '${height.toStringAsFixed(0)} سم';
  }

  /// تنسيق السعرات الحرارية
  static String formatCalories(double calories) {
    return '${calories.toStringAsFixed(0)} سعرة';
  }

  /// تنسيق الجرامات
  static String formatGrams(double grams) {
    return '${grams.toStringAsFixed(1)} جم';
  }

  /// حساب العمر من تاريخ الميلاد
  static int calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  /// حساب مؤشر كتلة الجسم
  static double calculateBMI(double weight, double height) {
    final heightInMeters = height / 100;
    return weight / (heightInMeters * heightInMeters);
  }

  /// تصنيف مؤشر كتلة الجسم
  static String getBMICategory(double bmi) {
    if (bmi < 18.5) {
      return 'نقص في الوزن';
    } else if (bmi < 25) {
      return 'وزن طبيعي';
    } else if (bmi < 30) {
      return 'زيادة في الوزن';
    } else {
      return 'سمنة';
    }
  }

  /// تحويل الجنس إلى نص
  static String getGenderText(String gender) {
    switch (gender.toLowerCase()) {
      case 'male':
      case 'ذكر':
        return AppStrings.male;
      case 'female':
      case 'أنثى':
        return AppStrings.female;
      default:
        return gender;
    }
  }

  /// تحويل حالة الموعد إلى نص
  static String getAppointmentStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return AppStrings.available;
      case 'booked':
        return AppStrings.booked;
      case 'completed':
        return AppStrings.completed;
      case 'cancelled':
        return AppStrings.cancelled;
      default:
        return status;
    }
  }

  /// تحويل حالة الموعد إلى لون
  static Color getAppointmentStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return Colors.green;
      case 'booked':
        return Colors.orange;
      case 'completed':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(AppConstants.emailRegex).hasMatch(email);
  }

  /// التحقق من صحة رقم الهاتف
  static bool isValidPhone(String phone) {
    return RegExp(AppConstants.phoneRegex).hasMatch(phone);
  }

  /// إنشاء معرف فريد
  static String generateUniqueId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// تحويل النص إلى أحرف كبيرة
  static String capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  /// تحويل النص إلى عنوان
  static String toTitleCase(String text) {
    return text.split(' ').map((word) => capitalize(word)).join(' ');
  }

  /// قطع النص
  static String truncateText(String text, int maxLength) {
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }

  /// إزالة المسافات الزائدة
  static String trimExtraSpaces(String text) {
    return text.replaceAll(RegExp(r'\s+'), ' ').trim();
  }

  /// التحقق من وجود اتصال بالإنترنت
  static Future<bool> hasInternetConnection() async {
    try {
      // يمكن تحسين هذا باستخدام مكتبة connectivity_plus
      return true; // مؤقت
    } catch (e) {
      return false;
    }
  }

  /// عرض رسالة خطأ
  static void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Cairo')),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
        ),
      ),
    );
  }

  /// عرض رسالة نجاح
  static void showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Cairo')),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
        ),
      ),
    );
  }

  /// عرض رسالة معلومات
  static void showInfoSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Cairo')),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppConstants.defaultRadius),
        ),
      ),
    );
  }

  /// عرض حوار تأكيد
  static Future<bool?> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
  }) {
    return showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(cancelText),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(confirmText),
              ),
            ],
          ),
    );
  }

  /// عرض حوار تحميل
  static void showLoadingDialog(BuildContext context, {String? message}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            content: Row(
              children: [
                SizedBox(
                  width: 40,
                  height: 40,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      const CircularProgressIndicator(),
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        child: ClipOval(
                          child: Image.asset(
                            'assets/images/logo.jpeg',
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(
                                Icons.health_and_safety_rounded,
                                size: 16,
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Text(message ?? AppStrings.loading),
              ],
            ),
          ),
    );
  }

  /// إخفاء حوار التحميل
  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context).pop();
  }

  /// التحقق من حجم الشاشة
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < AppConstants.mobileBreakpoint;
  }

  /// التحقق من حجم التابلت
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= AppConstants.mobileBreakpoint &&
        width < AppConstants.tabletBreakpoint;
  }

  /// التحقق من حجم سطح المكتب
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= AppConstants.tabletBreakpoint;
  }

  /// الحصول على عدد الأعمدة للشبكة
  static int getGridCrossAxisCount(BuildContext context) {
    if (isMobile(context)) {
      return 2;
    } else if (isTablet(context)) {
      return 3;
    } else {
      return 4;
    }
  }
}
