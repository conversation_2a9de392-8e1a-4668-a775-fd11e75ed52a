/// إعدادات المصادقة والتحقق
class AuthConfig {
  AuthConfig._();

  /// تفعيل/إلغاء تأكيد البريد الإلكتروني
  /// true = يتطلب تأكيد البريد الإلكتروني
  /// false = إنشاء الحساب مباشرة بدون تأكيد
  static const bool requireEmailVerification = false;

  /// رسالة التوضيح للمستخدم حسب النظام المفعل
  static String get registrationMessage {
    return requireEmailVerification
        ? 'سيتم إرسال كود التحقق إلى بريدك الإلكتروني'
        : 'سيتم إنشاء حسابك مباشرة';
  }

  /// نص زر التسجيل حسب النظام المفعل
  static String get registerButtonText {
    return requireEmailVerification
        ? 'إرسال كود التحقق'
        : 'إنشاء الحساب';
  }

  /// رسالة التحميل حسب النظام المفعل
  static String get loadingMessage {
    return requireEmailVerification
        ? 'جاري إرسال كود التحقق...'
        : 'جاري إنشاء الحساب...';
  }
}
