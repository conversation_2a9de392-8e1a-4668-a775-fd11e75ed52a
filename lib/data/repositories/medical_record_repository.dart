import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/entities/medical_record.dart';
import '../../core/errors/failures.dart';

/// Repository للتعامل مع بيانات السجلات الطبية في Supabase
class MedicalRecordRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// إنشاء سجل طبي جديد
  Future<MedicalRecord> createMedicalRecord({
    required String patientId,
    required String recordType,
    required String title,
    required String description,
    String? doctorId,
    String? doctorName,
    DateTime? recordDate,
    Map<String, dynamic>? data,
    List<String>? attachments,
    String? notes,
  }) async {
    try {
      final response = await _supabase
          .from('medical_records')
          .insert({
            'patient_id': patientId,
            'record_type': recordType,
            'title': title,
            'description': description,
            'doctor_id': doctorId,
            'doctor_name': doctor<PERSON><PERSON>,
            'record_date': (recordDate ?? DateTime.now()).toIso8601String(),
            'data': data,
            'attachments': attachments,
            'notes': notes,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .select()
          .single();

      return MedicalRecord.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في إنشاء السجل الطبي: ${e.toString()}');
    }
  }

  /// الحصول على السجلات الطبية للمريض
  Future<List<MedicalRecord>> getPatientMedicalRecords({
    required String patientId,
    String? recordType,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    int? offset,
  }) async {
    try {
      var query = _supabase
          .from('medical_records')
          .select()
          .eq('patient_id', patientId);

      if (recordType != null) {
        query = query.eq('record_type', recordType);
      }

      if (fromDate != null) {
        query = query.gte('record_date', fromDate.toIso8601String());
      }

      if (toDate != null) {
        query = query.lte('record_date', toDate.toIso8601String());
      }

      var orderedQuery = query.order('record_date', ascending: false);

      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      if (offset != null) {
        orderedQuery = orderedQuery.range(offset, offset + (limit ?? 10) - 1);
      }

      final response = await orderedQuery;

      return response.map((json) => MedicalRecord.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب السجلات الطبية: ${e.toString()}');
    }
  }

  /// الحصول على سجل طبي بواسطة ID
  Future<MedicalRecord?> getMedicalRecordById(String recordId) async {
    try {
      final response = await _supabase
          .from('medical_records')
          .select()
          .eq('id', recordId)
          .maybeSingle();

      if (response == null) return null;

      return MedicalRecord.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب السجل الطبي: ${e.toString()}');
    }
  }

  /// تحديث سجل طبي
  Future<MedicalRecord> updateMedicalRecord({
    required String recordId,
    String? recordType,
    String? title,
    String? description,
    String? doctorId,
    String? doctorName,
    DateTime? recordDate,
    Map<String, dynamic>? data,
    List<String>? attachments,
    String? notes,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (recordType != null) updateData['record_type'] = recordType;
      if (title != null) updateData['title'] = title;
      if (description != null) updateData['description'] = description;
      if (doctorId != null) updateData['doctor_id'] = doctorId;
      if (doctorName != null) updateData['doctor_name'] = doctorName;
      if (recordDate != null) updateData['record_date'] = recordDate.toIso8601String();
      if (data != null) updateData['data'] = data;
      if (attachments != null) updateData['attachments'] = attachments;
      if (notes != null) updateData['notes'] = notes;

      final response = await _supabase
          .from('medical_records')
          .update(updateData)
          .eq('id', recordId)
          .select()
          .single();

      return MedicalRecord.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في تحديث السجل الطبي: ${e.toString()}');
    }
  }

  /// حذف سجل طبي
  Future<void> deleteMedicalRecord(String recordId) async {
    try {
      await _supabase
          .from('medical_records')
          .delete()
          .eq('id', recordId);
    } catch (e) {
      throw ServerFailure(message: 'فشل في حذف السجل الطبي: ${e.toString()}');
    }
  }

  /// البحث في السجلات الطبية
  Future<List<MedicalRecord>> searchMedicalRecords({
    required String patientId,
    String? searchTerm,
    String? recordType,
    String? doctorName,
  }) async {
    try {
      var query = _supabase
          .from('medical_records')
          .select()
          .eq('patient_id', patientId);

      if (recordType != null) {
        query = query.eq('record_type', recordType);
      }

      if (doctorName != null) {
        query = query.ilike('doctor_name', '%$doctorName%');
      }

      if (searchTerm != null && searchTerm.isNotEmpty) {
        query = query.or('title.ilike.%$searchTerm%,description.ilike.%$searchTerm%,notes.ilike.%$searchTerm%');
      }

      final response = await query.order('record_date', ascending: false);

      return response.map((json) => MedicalRecord.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في البحث في السجلات الطبية: ${e.toString()}');
    }
  }

  /// الحصول على أنواع السجلات المتاحة
  Future<List<String>> getAvailableRecordTypes(String patientId) async {
    try {
      final response = await _supabase
          .from('medical_records')
          .select('record_type')
          .eq('patient_id', patientId);

      final types = response
          .map((item) => item['record_type'] as String)
          .toSet()
          .toList();

      types.sort();
      return types;
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب أنواع السجلات: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات السجلات الطبية
  Future<Map<String, dynamic>> getMedicalRecordStats(String patientId) async {
    try {
      final response = await _supabase
          .from('medical_records')
          .select('record_type, record_date')
          .eq('patient_id', patientId);

      final stats = {
        'total': response.length,
        'types': <String, int>{},
        'thisMonth': 0,
        'thisYear': 0,
      };

      final now = DateTime.now();
      final thisMonth = DateTime(now.year, now.month);
      final thisYear = DateTime(now.year);

      for (final record in response) {
        final recordType = record['record_type'] as String;
        final types = stats['types'] as Map<String, int>;
        types[recordType] = (types[recordType] ?? 0) + 1;

        final recordDate = DateTime.parse(record['record_date'] as String);
        if (recordDate.isAfter(thisMonth)) {
          stats['thisMonth'] = (stats['thisMonth'] as int) + 1;
        }
        if (recordDate.isAfter(thisYear)) {
          stats['thisYear'] = (stats['thisYear'] as int) + 1;
        }
      }

      return stats;
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب إحصائيات السجلات الطبية: ${e.toString()}');
    }
  }

  /// إضافة مرفق لسجل طبي
  Future<MedicalRecord> addAttachment({
    required String recordId,
    required String attachmentUrl,
  }) async {
    try {
      final record = await getMedicalRecordById(recordId);
      if (record == null) {
        throw ServerFailure(message: 'السجل الطبي غير موجود');
      }

      final currentAttachments = record.attachments ?? [];
      final updatedAttachments = [...currentAttachments, attachmentUrl];

      return await updateMedicalRecord(
        recordId: recordId,
        attachments: updatedAttachments,
      );
    } catch (e) {
      throw ServerFailure(message: 'فشل في إضافة المرفق: ${e.toString()}');
    }
  }

  /// إزالة مرفق من سجل طبي
  Future<MedicalRecord> removeAttachment({
    required String recordId,
    required String attachmentUrl,
  }) async {
    try {
      final record = await getMedicalRecordById(recordId);
      if (record == null) {
        throw ServerFailure(message: 'السجل الطبي غير موجود');
      }

      final currentAttachments = record.attachments ?? [];
      final updatedAttachments = currentAttachments
          .where((url) => url != attachmentUrl)
          .toList();

      return await updateMedicalRecord(
        recordId: recordId,
        attachments: updatedAttachments,
      );
    } catch (e) {
      throw ServerFailure(message: 'فشل في إزالة المرفق: ${e.toString()}');
    }
  }

  /// الحصول على السجلات الطبية حسب الطبيب
  Future<List<MedicalRecord>> getRecordsByDoctor({
    required String patientId,
    required String doctorId,
    int? limit,
  }) async {
    try {
      var query = _supabase
          .from('medical_records')
          .select()
          .eq('patient_id', patientId)
          .eq('doctor_id', doctorId)
          .order('record_date', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;

      return response.map((json) => MedicalRecord.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب سجلات الطبيب: ${e.toString()}');
    }
  }

  /// الحصول على آخر السجلات الطبية
  Future<List<MedicalRecord>> getRecentMedicalRecords({
    required String patientId,
    int limit = 5,
  }) async {
    try {
      final response = await _supabase
          .from('medical_records')
          .select()
          .eq('patient_id', patientId)
          .order('record_date', ascending: false)
          .limit(limit);

      return response.map((json) => MedicalRecord.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب آخر السجلات الطبية: ${e.toString()}');
    }
  }

  /// تصدير السجلات الطبية
  Future<List<Map<String, dynamic>>> exportMedicalRecords({
    required String patientId,
    DateTime? fromDate,
    DateTime? toDate,
    String? recordType,
  }) async {
    try {
      var query = _supabase
          .from('medical_records')
          .select()
          .eq('patient_id', patientId);

      if (recordType != null) {
        query = query.eq('record_type', recordType);
      }

      if (fromDate != null) {
        query = query.gte('record_date', fromDate.toIso8601String());
      }

      if (toDate != null) {
        query = query.lte('record_date', toDate.toIso8601String());
      }

      final response = await query.order('record_date', ascending: true);

      return response.map((json) => {
        'التاريخ': json['record_date'],
        'النوع': json['record_type'],
        'العنوان': json['title'],
        'الوصف': json['description'],
        'الطبيب': json['doctor_name'] ?? 'غير محدد',
        'الملاحظات': json['notes'] ?? '',
      }).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في تصدير السجلات الطبية: ${e.toString()}');
    }
  }
}
