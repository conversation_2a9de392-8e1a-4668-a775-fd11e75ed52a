import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/entities/product.dart';
import '../../domain/entities/category.dart';
import '../../core/errors/failures.dart';
import '../../core/utils/app_logger.dart';

/// Repository للتعامل مع بيانات المنتجات في Supabase
class ProductRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// الحصول على جميع المنتجات
  Future<List<Product>> getAllProducts({
    String? categoryId,
    bool? isAvailable,
    String? searchTerm,
    int? limit,
    int? offset,
    String? sortBy,
    bool ascending = true,
    String? category,
  }) async {
    try {
      AppLogger.info(
        '🛍️ Fetching products from database',
        category: LogCategory.database,
        data: {
          'categoryId': categoryId ?? 'all',
          'isAvailable': isAvailable?.toString() ?? 'all',
          'searchTerm': searchTerm ?? 'none',
          'limit': limit?.toString() ?? 'unlimited',
        },
      );

      // استعلام بسيط بدون ربط الفئات أولاً للتشخيص
      var query = _supabase.from('products').select('''
            id,
            name,
            description,
            price,
            stock,
            stock_quantity,
            product_code,
            discount_percentage,
            is_active,
            created_at,
            updated_at,
            category_id,
            images
          ''');

      // تطبيق الفلاتر
      if (categoryId != null && categoryId.isNotEmpty && categoryId != 'الكل') {
        query = query.eq('category_id', categoryId);
      }

      if (isAvailable == true) {
        query = query.eq('is_active', true).gt('stock', 0);
      } else if (isAvailable == false) {
        query = query.or('is_active.eq.false,stock.eq.0');
      }

      if (searchTerm != null && searchTerm.isNotEmpty) {
        query = query.or(
          'name.ilike.%$searchTerm%,description.ilike.%$searchTerm%',
        );
      }

      // الترتيب
      final orderBy = sortBy ?? 'created_at';
      var orderedQuery = query.order(orderBy, ascending: ascending);

      // التصفح
      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      if (offset != null) {
        orderedQuery = orderedQuery.range(offset, offset + (limit ?? 10) - 1);
      }

      final response = await orderedQuery;

      AppLogger.info(
        '✅ Products fetched successfully',
        category: LogCategory.database,
        data: {
          'count': response.length.toString(),
          'hasData': response.isNotEmpty.toString(),
        },
      );

      // معالجة البيانات بشكل مبسط للتشخيص
      AppLogger.info(
        '🔍 Raw response debug',
        category: LogCategory.database,
        data: {
          'response_length': response.length.toString(),
          'response_type': response.runtimeType.toString(),
          'first_item': response.isNotEmpty ? response[0].toString() : 'empty',
        },
      );

      final products =
          response.map((json) {
            // تسجيل تفصيلي لكل منتج
            AppLogger.info(
              '🔍 Processing product',
              category: LogCategory.database,
              data: {
                'json_keys': json.keys.toList().toString(),
                'images_type': json['images']?.runtimeType.toString() ?? 'null',
                'images_value': json['images']?.toString() ?? 'null',
              },
            );

            return Product.fromJson(json);
          }).toList();

      return products;
    } catch (e) {
      AppLogger.error(
        '❌ Failed to fetch products',
        category: LogCategory.database,
        error: e,
      );
      throw ServerFailure(message: 'فشل في جلب المنتجات: ${e.toString()}');
    }
  }

  /// الحصول على جميع الفئات
  Future<List<Category>> getAllCategories() async {
    try {
      AppLogger.info(
        '📂 Fetching categories from database',
        category: LogCategory.database,
      );

      final response = await _supabase
          .from('categories')
          .select()
          .eq('is_active', true)
          .order('name', ascending: true);

      AppLogger.info(
        '✅ Categories fetched successfully',
        category: LogCategory.database,
        data: {'count': response.length.toString()},
      );

      return response.map((json) => Category.fromJson(json)).toList();
    } catch (e) {
      AppLogger.error(
        '❌ Failed to fetch categories',
        category: LogCategory.database,
        error: e,
      );
      throw ServerFailure(message: 'فشل في جلب الفئات: ${e.toString()}');
    }
  }

  /// الحصول على منتج بواسطة ID
  Future<Product?> getProductById(String productId) async {
    try {
      final response =
          await _supabase
              .from('products')
              .select('''
                id,
                name,
                description,
                price,
                stock,
                stock_quantity,
                product_code,
                discount_percentage,
                is_active,
                created_at,
                updated_at,
                category_id,
                images,
                categories!inner(
                  id,
                  name
                )
              ''')
              .eq('id', productId)
              .maybeSingle();

      if (response == null) return null;

      // إضافة اسم الفئة من البيانات المربوطة
      if (response['categories'] != null &&
          response['categories'] is Map<String, dynamic>) {
        final categoryData = response['categories'] as Map<String, dynamic>;
        response['category_name'] = categoryData['name']?.toString();
      }

      return Product.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب المنتج: ${e.toString()}');
    }
  }

  /// الحصول على المنتجات المميزة
  Future<List<Product>> getFeaturedProducts({int? limit}) async {
    try {
      var query = _supabase
          .from('products')
          .select()
          .eq('is_featured', true)
          .eq('is_active', true)
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(
        message: 'فشل في جلب المنتجات المميزة: ${e.toString()}',
      );
    }
  }

  /// الحصول على المنتجات حسب الفئة
  Future<List<Product>> getProductsByCategory(
    String category, {
    int? limit,
  }) async {
    try {
      var query = _supabase
          .from('products')
          .select()
          .eq('category_id', category)
          .eq('is_active', true)
          .order('name', ascending: true);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب منتجات الفئة: ${e.toString()}');
    }
  }

  /// الحصول على الفئات المتاحة
  Future<List<String>> getAvailableCategories() async {
    try {
      final response = await _supabase
          .from('products')
          .select('category')
          .eq('is_available', true);

      final categories =
          response.map((item) => item['category'] as String).toSet().toList();

      categories.sort();
      return categories;
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب الفئات: ${e.toString()}');
    }
  }

  /// البحث في المنتجات
  Future<List<Product>> searchProducts({
    required String searchTerm,
    String? category,
    double? minPrice,
    double? maxPrice,
    int? limit,
  }) async {
    try {
      var query = _supabase.from('products').select().eq('is_available', true);

      // البحث في الاسم والوصف
      if (searchTerm.isNotEmpty) {
        query = query.or(
          'name.ilike.%$searchTerm%,description.ilike.%$searchTerm%',
        );
      }

      // فلتر الفئة
      if (category != null && category.isNotEmpty) {
        query = query.eq('category', category);
      }

      // فلتر السعر
      if (minPrice != null) {
        query = query.gte('price', minPrice);
      }

      if (maxPrice != null) {
        query = query.lte('price', maxPrice);
      }

      // الترتيب والحد الأقصى
      var orderedQuery = query.order('name', ascending: true);

      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      final response = await orderedQuery;

      return response.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في البحث عن المنتجات: ${e.toString()}');
    }
  }

  /// إنشاء منتج جديد (للإدارة)
  Future<Product> createProduct({
    required String name,
    required String description,
    required double price,
    required String category,
    String? imageUrl,
    bool isAvailable = true,
    bool isFeatured = false,
    Map<String, dynamic>? nutritionInfo,
    List<String>? ingredients,
    String? usage,
    int? stockQuantity,
  }) async {
    try {
      final response =
          await _supabase
              .from('products')
              .insert({
                'name': name,
                'description': description,
                'price': price,
                'category': category,
                'image_url': imageUrl,
                'is_available': isAvailable,
                'is_featured': isFeatured,
                'nutrition_info': nutritionInfo,
                'ingredients': ingredients,
                'usage': usage,
                'stock_quantity': stockQuantity,
                'created_at': DateTime.now().toIso8601String(),
                'updated_at': DateTime.now().toIso8601String(),
              })
              .select()
              .single();

      return Product.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في إنشاء المنتج: ${e.toString()}');
    }
  }

  /// تحديث منتج (للإدارة)
  Future<Product> updateProduct({
    required String productId,
    String? name,
    String? description,
    double? price,
    String? category,
    String? imageUrl,
    bool? isAvailable,
    bool? isFeatured,
    Map<String, dynamic>? nutritionInfo,
    List<String>? ingredients,
    String? usage,
    int? stockQuantity,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (name != null) updateData['name'] = name;
      if (description != null) updateData['description'] = description;
      if (price != null) updateData['price'] = price;
      if (category != null) updateData['category'] = category;
      if (imageUrl != null) updateData['image_url'] = imageUrl;
      if (isAvailable != null) updateData['is_available'] = isAvailable;
      if (isFeatured != null) updateData['is_featured'] = isFeatured;
      if (nutritionInfo != null) updateData['nutrition_info'] = nutritionInfo;
      if (ingredients != null) updateData['ingredients'] = ingredients;
      if (usage != null) updateData['usage'] = usage;
      if (stockQuantity != null) updateData['stock_quantity'] = stockQuantity;

      final response =
          await _supabase
              .from('products')
              .update(updateData)
              .eq('id', productId)
              .select()
              .single();

      return Product.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في تحديث المنتج: ${e.toString()}');
    }
  }

  /// حذف منتج (للإدارة)
  Future<void> deleteProduct(String productId) async {
    try {
      await _supabase.from('products').delete().eq('id', productId);
    } catch (e) {
      throw ServerFailure(message: 'فشل في حذف المنتج: ${e.toString()}');
    }
  }

  /// تحديث كمية المخزون
  Future<Product> updateStock({
    required String productId,
    required int quantity,
  }) async {
    try {
      final response =
          await _supabase
              .from('products')
              .update({
                'stock_quantity': quantity,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', productId)
              .select()
              .single();

      return Product.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في تحديث المخزون: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات المنتجات
  Future<Map<String, dynamic>> getProductStats() async {
    try {
      final response = await _supabase
          .from('products')
          .select('category, is_available, price');

      final stats = {
        'total': response.length,
        'available': 0,
        'categories': <String, int>{},
        'averagePrice': 0.0,
      };

      double totalPrice = 0;
      for (final product in response) {
        if (product['is_available'] == true) {
          stats['available'] = (stats['available'] as int) + 1;
        }

        final category = product['category'] as String;
        final categories = stats['categories'] as Map<String, int>;
        categories[category] = (categories[category] ?? 0) + 1;

        totalPrice += (product['price'] as num).toDouble();
      }

      if (response.isNotEmpty) {
        stats['averagePrice'] = totalPrice / response.length;
      }

      return stats;
    } catch (e) {
      throw ServerFailure(
        message: 'فشل في جلب إحصائيات المنتجات: ${e.toString()}',
      );
    }
  }
}
