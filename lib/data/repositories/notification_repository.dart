import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/utils/app_logger.dart';
import '../models/notification_model.dart';

class NotificationRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// الحصول على إشعارات المستخدم
  Future<List<NotificationModel>> getUserNotifications({
    String? userId,
    int limit = 50,
    int offset = 0,
    bool? isRead,
  }) async {
    try {
      final currentUserId = userId ?? _supabase.auth.currentUser?.id;

      if (currentUserId == null) {
        AppLogger.warning(
          'No user ID provided for notifications',
          category: LogCategory.notification,
        );
        return [];
      }

      // بناء الاستعلام بطريقة متسلسلة
      var queryBuilder = _supabase
          .from('notifications_log')
          .select('*')
          .eq('user_id', currentUserId);

      // فلترة حسب حالة القراءة إذا تم تحديدها
      if (isRead != null) {
        queryBuilder = queryBuilder.eq('is_read', isRead);
      }

      // ترتيب النتائج
      var orderedQuery = queryBuilder.order('created_at', ascending: false);

      // تطبيق الحد والإزاحة
      if (limit > 0) {
        orderedQuery = orderedQuery.limit(limit);
      }
      if (offset > 0) {
        orderedQuery = orderedQuery.range(offset, offset + limit - 1);
      }

      final response = await orderedQuery;

      final notifications = <NotificationModel>[];
      for (final json in response) {
        try {
          notifications.add(NotificationModel.fromJson(json));
        } catch (e) {
          AppLogger.warning(
            'Failed to parse notification',
            category: LogCategory.notification,
            data: {'json': json.toString()},
            error: e,
          );
        }
      }

      AppLogger.info(
        'Retrieved ${notifications.length} notifications for user',
        category: LogCategory.notification,
        data: {
          'userId': currentUserId,
          'count': notifications.length.toString(),
          'isRead': isRead?.toString() ?? 'all',
        },
      );

      return notifications;
    } catch (e) {
      AppLogger.error(
        'Failed to get user notifications',
        category: LogCategory.notification,
        error: e,
      );
      return [];
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  Future<int> getUnreadNotificationsCount({String? userId}) async {
    try {
      final currentUserId = userId ?? _supabase.auth.currentUser?.id;

      if (currentUserId == null) {
        return 0;
      }

      // استخدام طريقة بديلة للعد
      final response = await _supabase
          .from('notifications_log')
          .select('id')
          .eq('user_id', currentUserId)
          .eq('is_read', false);

      final count = response.length;

      AppLogger.info(
        'Unread notifications count: $count',
        category: LogCategory.notification,
        data: {'userId': currentUserId, 'count': count.toString()},
      );

      return count;
    } catch (e) {
      AppLogger.error(
        'Failed to get unread notifications count',
        category: LogCategory.notification,
        error: e,
      );
      return 0;
    }
  }

  /// تحديد إشعار كمقروء
  Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      await _supabase
          .from('notifications_log')
          .update({'is_read': true})
          .eq('id', notificationId);

      AppLogger.info(
        'Notification marked as read',
        category: LogCategory.notification,
        data: {'notificationId': notificationId},
      );

      return true;
    } catch (e) {
      AppLogger.error(
        'Failed to mark notification as read',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<bool> markAllNotificationsAsRead({String? userId}) async {
    try {
      final currentUserId = userId ?? _supabase.auth.currentUser?.id;

      if (currentUserId == null) {
        return false;
      }

      await _supabase
          .from('notifications_log')
          .update({'is_read': true})
          .eq('user_id', currentUserId)
          .eq('is_read', false);

      AppLogger.info(
        'All notifications marked as read',
        category: LogCategory.notification,
        data: {'userId': currentUserId},
      );

      return true;
    } catch (e) {
      AppLogger.error(
        'Failed to mark all notifications as read',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// حذف إشعار
  Future<bool> deleteNotification(String notificationId) async {
    try {
      await _supabase
          .from('notifications_log')
          .delete()
          .eq('id', notificationId);

      AppLogger.info(
        'Notification deleted',
        category: LogCategory.notification,
        data: {'notificationId': notificationId},
      );

      return true;
    } catch (e) {
      AppLogger.error(
        'Failed to delete notification',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// حذف جميع الإشعارات المقروءة
  Future<bool> deleteReadNotifications({String? userId}) async {
    try {
      final currentUserId = userId ?? _supabase.auth.currentUser?.id;

      if (currentUserId == null) {
        return false;
      }

      await _supabase
          .from('notifications_log')
          .delete()
          .eq('user_id', currentUserId)
          .eq('is_read', true);

      AppLogger.info(
        'Read notifications deleted',
        category: LogCategory.notification,
        data: {'userId': currentUserId},
      );

      return true;
    } catch (e) {
      AppLogger.error(
        'Failed to delete read notifications',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// إنشاء إشعار جديد
  Future<bool> createNotification({
    required String userId,
    required String type,
    required String title,
    String? message,
    String? content,
    String? description,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
  }) async {
    try {
      final notificationData = <String, dynamic>{
        'user_id': userId,
        'type': type,
        'title': title,
        'is_read': false,
        'created_at': DateTime.now().toIso8601String(),
      };

      // إضافة الحقول الاختيارية فقط إذا كانت موجودة
      if (message != null && message.isNotEmpty) {
        notificationData['message'] = message;
      }
      if (content != null && content.isNotEmpty) {
        notificationData['content'] = content;
      }
      if (description != null && description.isNotEmpty) {
        notificationData['description'] = description;
      }
      if (imageUrl != null && imageUrl.isNotEmpty) {
        notificationData['image_url'] = imageUrl;
      }
      if (actionUrl != null && actionUrl.isNotEmpty) {
        notificationData['action_url'] = actionUrl;
      }

      // تحويل data إلى JSON string إذا كان موجود
      if (data != null && data.isNotEmpty) {
        try {
          notificationData['data'] = data;
        } catch (e) {
          AppLogger.warning(
            'Failed to serialize notification data',
            category: LogCategory.notification,
            error: e,
          );
        }
      }

      await _supabase.from('notifications_log').insert(notificationData);

      AppLogger.info(
        'Notification created successfully',
        category: LogCategory.notification,
        data: {'userId': userId, 'type': type, 'title': title},
      );

      return true;
    } catch (e) {
      AppLogger.error(
        'Failed to create notification',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// البحث في الإشعارات
  Future<List<NotificationModel>> searchNotifications({
    required String query,
    String? userId,
    String? type,
    int limit = 20,
  }) async {
    try {
      final currentUserId = userId ?? _supabase.auth.currentUser?.id;

      if (currentUserId == null) {
        return [];
      }

      // بناء الاستعلام بطريقة متسلسلة
      var queryBuilder = _supabase
          .from('notifications_log')
          .select('*')
          .eq('user_id', currentUserId);

      // فلترة حسب النوع إذا تم تحديده
      if (type != null && type.isNotEmpty) {
        queryBuilder = queryBuilder.eq('type', type);
      }

      // ترتيب وتحديد العدد
      final response = await queryBuilder
          .order('created_at', ascending: false)
          .limit(limit);

      final notifications = <NotificationModel>[];
      for (final json in response) {
        try {
          final notification = NotificationModel.fromJson(json);

          // تطبيق البحث النصي محلياً
          final searchLower = query.toLowerCase();
          if (notification.title.toLowerCase().contains(searchLower) ||
              notification.bodyText.toLowerCase().contains(searchLower)) {
            notifications.add(notification);
          }
        } catch (e) {
          AppLogger.warning(
            'Failed to parse notification in search',
            category: LogCategory.notification,
            error: e,
          );
        }
      }

      AppLogger.info(
        'Search completed',
        category: LogCategory.notification,
        data: {
          'query': query,
          'results': notifications.length.toString(),
          'type': type ?? 'all',
        },
      );

      return notifications;
    } catch (e) {
      AppLogger.error(
        'Failed to search notifications',
        category: LogCategory.notification,
        error: e,
      );
      return [];
    }
  }

  /// الحصول على إحصائيات الإشعارات
  Future<Map<String, int>> getNotificationStats({String? userId}) async {
    try {
      final currentUserId = userId ?? _supabase.auth.currentUser?.id;

      if (currentUserId == null) {
        return {};
      }

      // الحصول على جميع الإشعارات للمستخدم
      final allNotifications = await _supabase
          .from('notifications_log')
          .select('type, is_read')
          .eq('user_id', currentUserId);

      // حساب الإحصائيات محلياً
      final stats = <String, int>{'total': 0, 'unread': 0, 'read': 0};

      final typeStats = <String, int>{};

      for (final notification in allNotifications) {
        stats['total'] = (stats['total'] ?? 0) + 1;

        final isRead = notification['is_read'] as bool? ?? false;
        if (isRead) {
          stats['read'] = (stats['read'] ?? 0) + 1;
        } else {
          stats['unread'] = (stats['unread'] ?? 0) + 1;
        }

        final type = notification['type'] as String? ?? 'unknown';
        typeStats[type] = (typeStats[type] ?? 0) + 1;
      }

      // دمج إحصائيات الأنواع
      stats.addAll(typeStats);

      AppLogger.info(
        'Notification stats retrieved',
        category: LogCategory.notification,
        data: stats.map((key, value) => MapEntry(key, value.toString())),
      );

      return stats;
    } catch (e) {
      AppLogger.error(
        'Failed to get notification stats',
        category: LogCategory.notification,
        error: e,
      );
      return {};
    }
  }

  /// التحقق من وجود جدول الإشعارات
  Future<bool> checkNotificationsTableExists() async {
    try {
      await _supabase.from('notifications_log').select('id').limit(1);

      return true;
    } catch (e) {
      AppLogger.error(
        'Notifications table does not exist or is not accessible',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }

  /// إنشاء إشعار اختبار
  Future<bool> createTestNotification({String? userId}) async {
    final currentUserId = userId ?? _supabase.auth.currentUser?.id;

    if (currentUserId == null) {
      return false;
    }

    return await createNotification(
      userId: currentUserId,
      type: 'test',
      title: 'إشعار اختبار',
      message: 'هذا إشعار اختبار للتأكد من عمل النظام',
    );
  }

  /// حذف جميع الإشعارات (للاختبار فقط)
  Future<bool> deleteAllNotifications({String? userId}) async {
    try {
      final currentUserId = userId ?? _supabase.auth.currentUser?.id;

      if (currentUserId == null) {
        return false;
      }

      await _supabase
          .from('notifications_log')
          .delete()
          .eq('user_id', currentUserId);

      AppLogger.info(
        'All notifications deleted',
        category: LogCategory.notification,
        data: {'userId': currentUserId},
      );

      return true;
    } catch (e) {
      AppLogger.error(
        'Failed to delete all notifications',
        category: LogCategory.notification,
        error: e,
      );
      return false;
    }
  }
}
