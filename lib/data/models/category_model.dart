class CategoryModel {
  final String id;
  final String name;
  final String? description;
  final String type;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String icon;
  final String color;

  CategoryModel({
    required this.id,
    required this.name,
    this.description,
    required this.type,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.icon = 'category',
    this.color = '#2196F3',
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) {
    return CategoryModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      type: json['type'] ?? '',
      isActive: json['is_active'] ?? true,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      icon: json['icon'] ?? 'category',
      color: json['color'] ?? '#2196F3',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type,
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'icon': icon,
      'color': color,
    };
  }

  CategoryModel copyWith({
    String? id,
    String? name,
    String? description,
    String? type,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? icon,
    String? color,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      icon: icon ?? this.icon,
      color: color ?? this.color,
    );
  }

  // Helper methods
  bool get hasDescription => description != null && description!.isNotEmpty;
  
  String get displayName => name.isNotEmpty ? name : 'فئة غير محددة';
  String get displayType => type.isNotEmpty ? type : 'نوع غير محدد';
  
  String get formattedCreatedDate {
    if (createdAt == null) return 'غير محدد';
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }
  
  String get statusText => isActive ? 'نشط' : 'غير نشط';
  
  // Category type helpers
  bool get isProductCategory => type.toLowerCase() == 'product';
  bool get isServiceCategory => type.toLowerCase() == 'service';
  bool get isArticleCategory => type.toLowerCase() == 'article';
  bool get isMedicalCategory => type.toLowerCase() == 'medical';
  
  @override
  String toString() {
    return 'CategoryModel(id: $id, name: $name, type: $type, isActive: $isActive)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategoryModel && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}
