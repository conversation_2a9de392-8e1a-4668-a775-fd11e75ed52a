class AppointmentModel {
  final String id;
  final String? patientId;
  final DateTime appointmentDate;
  final String? timeSlotId;
  final String status;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? employeeId;
  final String appointmentType;
  final int durationMinutes;
  final String? sessionNotes;
  final DateTime? nextAppointmentDate;
  final double consultationFee;
  final double paidAmount;
  final double remainingAmount;
  final bool isMultipleBooking;
  final String? multipleBookingGroupId;
  final int? bookingSequence;

  AppointmentModel({
    required this.id,
    this.patientId,
    required this.appointmentDate,
    this.timeSlotId,
    this.status = 'available',
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.employeeId,
    this.appointmentType = 'consultation',
    this.durationMinutes = 30,
    this.sessionNotes,
    this.nextAppointmentDate,
    this.consultationFee = 0.0,
    this.paidAmount = 0.0,
    this.remainingAmount = 0.0,
    this.isMultipleBooking = false,
    this.multipleBookingGroupId,
    this.bookingSequence,
  });

  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    return AppointmentModel(
      id: json['id'] ?? '',
      patientId: json['patient_id'],
      appointmentDate: json['appointment_date'] != null 
          ? DateTime.parse(json['appointment_date']) 
          : DateTime.now(),
      timeSlotId: json['time_slot_id'],
      status: json['status'] ?? 'available',
      notes: json['notes'],
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      employeeId: json['employee_id'],
      appointmentType: json['appointment_type'] ?? 'consultation',
      durationMinutes: json['duration_minutes'] ?? 30,
      sessionNotes: json['session_notes'],
      nextAppointmentDate: json['next_appointment_date'] != null 
          ? DateTime.tryParse(json['next_appointment_date']) 
          : null,
      consultationFee: double.tryParse(json['consultation_fee'].toString()) ?? 0.0,
      paidAmount: double.tryParse(json['paid_amount'].toString()) ?? 0.0,
      remainingAmount: double.tryParse(json['remaining_amount'].toString()) ?? 0.0,
      isMultipleBooking: json['is_multiple_booking'] ?? false,
      multipleBookingGroupId: json['multiple_booking_group_id'],
      bookingSequence: json['booking_sequence'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'appointment_date': appointmentDate.toIso8601String().split('T')[0],
      'time_slot_id': timeSlotId,
      'status': status,
      'notes': notes,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'employee_id': employeeId,
      'appointment_type': appointmentType,
      'duration_minutes': durationMinutes,
      'session_notes': sessionNotes,
      'next_appointment_date': nextAppointmentDate?.toIso8601String().split('T')[0],
      'consultation_fee': consultationFee,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'is_multiple_booking': isMultipleBooking,
      'multiple_booking_group_id': multipleBookingGroupId,
      'booking_sequence': bookingSequence,
    };
  }

  AppointmentModel copyWith({
    String? id,
    String? patientId,
    DateTime? appointmentDate,
    String? timeSlotId,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? employeeId,
    String? appointmentType,
    int? durationMinutes,
    String? sessionNotes,
    DateTime? nextAppointmentDate,
    double? consultationFee,
    double? paidAmount,
    double? remainingAmount,
    bool? isMultipleBooking,
    String? multipleBookingGroupId,
    int? bookingSequence,
  }) {
    return AppointmentModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      timeSlotId: timeSlotId ?? this.timeSlotId,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      employeeId: employeeId ?? this.employeeId,
      appointmentType: appointmentType ?? this.appointmentType,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      sessionNotes: sessionNotes ?? this.sessionNotes,
      nextAppointmentDate: nextAppointmentDate ?? this.nextAppointmentDate,
      consultationFee: consultationFee ?? this.consultationFee,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      isMultipleBooking: isMultipleBooking ?? this.isMultipleBooking,
      multipleBookingGroupId: multipleBookingGroupId ?? this.multipleBookingGroupId,
      bookingSequence: bookingSequence ?? this.bookingSequence,
    );
  }

  // Helper methods
  bool get hasPatient => patientId != null && patientId!.isNotEmpty;
  bool get hasTimeSlot => timeSlotId != null && timeSlotId!.isNotEmpty;
  bool get hasEmployee => employeeId != null && employeeId!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  bool get hasSessionNotes => sessionNotes != null && sessionNotes!.isNotEmpty;
  bool get hasNextAppointment => nextAppointmentDate != null;
  bool get hasFee => consultationFee > 0;
  bool get hasPayment => paidAmount > 0;
  bool get hasRemainingAmount => remainingAmount > 0;
  
  // Status helpers
  bool get isAvailable => status.toLowerCase() == 'available';
  bool get isBooked => status.toLowerCase() == 'booked';
  bool get isCompleted => status.toLowerCase() == 'completed';
  bool get isCancelled => status.toLowerCase() == 'cancelled';
  bool get isNoShow => status.toLowerCase() == 'no_show';
  bool get isRescheduled => status.toLowerCase() == 'rescheduled';
  
  String get statusText {
    switch (status.toLowerCase()) {
      case 'available':
        return 'متاح';
      case 'booked':
        return 'محجوز';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'no_show':
        return 'لم يحضر';
      case 'rescheduled':
        return 'معاد جدولة';
      default:
        return 'غير محدد';
    }
  }
  
  // Type helpers
  bool get isConsultation => appointmentType.toLowerCase() == 'consultation';
  bool get isFollowUp => appointmentType.toLowerCase() == 'follow_up';
  bool get isEmergency => appointmentType.toLowerCase() == 'emergency';
  bool get isCheckup => appointmentType.toLowerCase() == 'checkup';
  
  String get appointmentTypeText {
    switch (appointmentType.toLowerCase()) {
      case 'consultation':
        return 'استشارة';
      case 'follow_up':
        return 'متابعة';
      case 'emergency':
        return 'طوارئ';
      case 'checkup':
        return 'فحص';
      default:
        return 'غير محدد';
    }
  }
  
  // Date helpers
  String get formattedAppointmentDate {
    return '${appointmentDate.day}/${appointmentDate.month}/${appointmentDate.year}';
  }
  
  String get formattedNextAppointmentDate {
    if (nextAppointmentDate == null) return 'غير محدد';
    return '${nextAppointmentDate!.day}/${nextAppointmentDate!.month}/${nextAppointmentDate!.year}';
  }
  
  bool get isToday {
    final now = DateTime.now();
    return appointmentDate.year == now.year &&
           appointmentDate.month == now.month &&
           appointmentDate.day == now.day;
  }
  
  bool get isPast => appointmentDate.isBefore(DateTime.now());
  bool get isFuture => appointmentDate.isAfter(DateTime.now());
  
  // Payment helpers
  String get formattedConsultationFee => '${consultationFee.toStringAsFixed(2)} جنيه';
  String get formattedPaidAmount => '${paidAmount.toStringAsFixed(2)} جنيه';
  String get formattedRemainingAmount => '${remainingAmount.toStringAsFixed(2)} جنيه';
  
  bool get isFullyPaid => paidAmount >= consultationFee;
  bool get isPartiallyPaid => paidAmount > 0 && paidAmount < consultationFee;
  bool get isUnpaid => paidAmount == 0;
  
  String get paymentStatus {
    if (isFullyPaid) return 'مدفوع بالكامل';
    if (isPartiallyPaid) return 'مدفوع جزئياً';
    return 'غير مدفوع';
  }
  
  // Duration helpers
  String get formattedDuration {
    if (durationMinutes >= 60) {
      final hours = durationMinutes ~/ 60;
      final minutes = durationMinutes % 60;
      if (minutes == 0) {
        return '$hours ${hours == 1 ? 'ساعة' : 'ساعات'}';
      } else {
        return '$hours ${hours == 1 ? 'ساعة' : 'ساعات'} و $minutes ${minutes == 1 ? 'دقيقة' : 'دقائق'}';
      }
    } else {
      return '$durationMinutes ${durationMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    }
  }
  
  // Display helpers for UI
  String get displayDate => formattedAppointmentDate;
  
  String get displayTime {
    // إذا كان لدينا timeSlotId، يمكننا استخدامه لاحقاً
    // حالياً سنعرض وقت افتراضي أو من البيانات المتاحة
    final hour = appointmentDate.hour;
    final minute = appointmentDate.minute;
    if (hour == 0 && minute == 0) {
      return 'غير محدد';
    }
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }
  
  String get displayAppointmentType => appointmentTypeText;
  String get displayStatus => statusText;
  
  // Time until appointment for future appointments
  String get timeUntilAppointment {
    if (!isFuture) return '';
    
    final now = DateTime.now();
    final difference = appointmentDate.difference(now);
    
    if (difference.inDays > 0) {
      return 'خلال ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'خلال ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'خلال ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }
  
  // Can cancel appointment (only future appointments that are not completed/cancelled)
  bool get canCancel {
    return isFuture && !isCompleted && !isCancelled && !isNoShow;
  }
  
  // Payment helpers
  bool get hasPartialPayment => isPartiallyPaid;
  
  @override
  String toString() {
    return 'AppointmentModel(id: $id, date: $formattedAppointmentDate, status: $statusText, type: $appointmentTypeText)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppointmentModel && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}
