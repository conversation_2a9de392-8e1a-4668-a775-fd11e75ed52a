class InvoiceItemModel {
  final String id;
  final String? invoiceId;
  final String? productId;
  final String productName;
  final String? productCode;
  final int quantity;
  final double unitPrice;
  final double totalPrice;
  final double discountPercentage;
  final double discountAmount;
  final double finalPrice;
  final DateTime? createdAt;

  InvoiceItemModel({
    required this.id,
    this.invoiceId,
    this.productId,
    required this.productName,
    this.productCode,
    this.quantity = 1,
    required this.unitPrice,
    required this.totalPrice,
    this.discountPercentage = 0.0,
    this.discountAmount = 0.0,
    required this.finalPrice,
    this.createdAt,
  });

  factory InvoiceItemModel.fromJson(Map<String, dynamic> json) {
    return InvoiceItemModel(
      id: json['id'] ?? '',
      invoiceId: json['invoice_id'],
      productId: json['product_id'],
      productName: json['product_name'] ?? '',
      productCode: json['product_code'],
      quantity: json['quantity'] ?? 1,
      unitPrice: double.tryParse(json['unit_price'].toString()) ?? 0.0,
      totalPrice: double.tryParse(json['total_price'].toString()) ?? 0.0,
      discountPercentage:
          double.tryParse(json['discount_percentage'].toString()) ?? 0.0,
      discountAmount:
          double.tryParse(json['discount_amount'].toString()) ?? 0.0,
      finalPrice: double.tryParse(json['final_price'].toString()) ?? 0.0,
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoice_id': invoiceId,
      'product_id': productId,
      'product_name': productName,
      'product_code': productCode,
      'quantity': quantity,
      'unit_price': unitPrice,
      'total_price': totalPrice,
      'discount_percentage': discountPercentage,
      'discount_amount': discountAmount,
      'final_price': finalPrice,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  InvoiceItemModel copyWith({
    String? id,
    String? invoiceId,
    String? productId,
    String? productName,
    String? productCode,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
    double? discountPercentage,
    double? discountAmount,
    double? finalPrice,
    DateTime? createdAt,
  }) {
    return InvoiceItemModel(
      id: id ?? this.id,
      invoiceId: invoiceId ?? this.invoiceId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productCode: productCode ?? this.productCode,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      discountAmount: discountAmount ?? this.discountAmount,
      finalPrice: finalPrice ?? this.finalPrice,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Helper methods
  bool get hasProductCode => productCode != null && productCode!.isNotEmpty;
  bool get hasDiscount => discountPercentage > 0 || discountAmount > 0;

  String get displayProductName =>
      productName.isNotEmpty ? productName : 'منتج غير محدد';
  String get displayProductCode => productCode ?? 'غير محدد';

  String get formattedUnitPrice => '${unitPrice.toStringAsFixed(2)} جنيه';
  String get formattedTotalPrice => '${totalPrice.toStringAsFixed(2)} جنيه';
  String get formattedFinalPrice => '${finalPrice.toStringAsFixed(2)} جنيه';
  String get formattedDiscountAmount =>
      '${discountAmount.toStringAsFixed(2)} جنيه';
  String get formattedDiscountPercentage =>
      '${discountPercentage.toStringAsFixed(1)}%';

  String get quantityText => '$quantity ${quantity == 1 ? 'قطعة' : 'قطع'}';

  // Calculations
  double get calculatedTotalPrice => unitPrice * quantity;
  double get calculatedDiscountAmount {
    if (discountPercentage > 0) {
      return calculatedTotalPrice * (discountPercentage / 100);
    }
    return discountAmount;
  }

  double get calculatedFinalPrice =>
      calculatedTotalPrice - calculatedDiscountAmount;

  // Validation
  bool get isValid {
    return productName.isNotEmpty &&
        quantity > 0 &&
        unitPrice >= 0 &&
        finalPrice >= 0;
  }

  String get formattedCreatedDate {
    if (createdAt == null) return 'غير محدد';
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }

  // Discount helpers
  String get discountText {
    if (!hasDiscount) return 'بدون خصم';
    if (discountPercentage > 0) {
      return 'خصم $formattedDiscountPercentage';
    }
    return 'خصم $formattedDiscountAmount';
  }

  @override
  String toString() {
    return 'InvoiceItemModel(id: $id, product: $productName, quantity: $quantity, finalPrice: $formattedFinalPrice)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvoiceItemModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
