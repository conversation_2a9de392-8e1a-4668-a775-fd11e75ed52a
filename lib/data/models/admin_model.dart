class AdminModel {
  final String id;
  final String name;
  final String email;
  final String? role;
  final bool isActive;
  final DateTime? lastLogin;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? employeeType;
  final String? specializationId;
  final String? phone;
  final String? address;
  final DateTime? hireDate;
  final double? salary;
  final String? notes;
  final String? profileImageUrl;
  final String? specializationName;

  AdminModel({
    required this.id,
    required this.name,
    required this.email,
    this.role = 'admin',
    this.isActive = true,
    this.lastLogin,
    this.createdAt,
    this.updatedAt,
    this.employeeType = 'admin',
    this.specializationId,
    this.phone,
    this.address,
    this.hireDate,
    this.salary,
    this.notes,
    this.profileImageUrl,
    this.specializationName,
  });

  factory AdminModel.fromJson(Map<String, dynamic> json) {
    return AdminModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      role: json['role'] ?? 'admin',
      isActive: json['is_active'] ?? true,
      lastLogin: json['last_login'] != null 
          ? DateTime.tryParse(json['last_login']) 
          : null,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      employeeType: json['employee_type'] ?? 'admin',
      specializationId: json['specialization_id'],
      phone: json['phone'],
      address: json['address'],
      hireDate: json['hire_date'] != null 
          ? DateTime.tryParse(json['hire_date']) 
          : null,
      salary: json['salary'] != null 
          ? double.tryParse(json['salary'].toString()) 
          : null,
      notes: json['notes'],
      profileImageUrl: json['profile_image_url'],
      specializationName: json['specialization_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': role,
      'is_active': isActive,
      'last_login': lastLogin?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'employee_type': employeeType,
      'specialization_id': specializationId,
      'phone': phone,
      'address': address,
      'hire_date': hireDate?.toIso8601String().split('T')[0],
      'salary': salary,
      'notes': notes,
      'profile_image_url': profileImageUrl,
      'specialization_name': specializationName,
    };
  }

  AdminModel copyWith({
    String? id,
    String? name,
    String? email,
    String? role,
    bool? isActive,
    DateTime? lastLogin,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? employeeType,
    String? specializationId,
    String? phone,
    String? address,
    DateTime? hireDate,
    double? salary,
    String? notes,
    String? profileImageUrl,
    String? specializationName,
  }) {
    return AdminModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      lastLogin: lastLogin ?? this.lastLogin,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      employeeType: employeeType ?? this.employeeType,
      specializationId: specializationId ?? this.specializationId,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      hireDate: hireDate ?? this.hireDate,
      salary: salary ?? this.salary,
      notes: notes ?? this.notes,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      specializationName: specializationName ?? this.specializationName,
    );
  }

  // Helper methods
  bool get hasSpecialization => specializationId != null && specializationId!.isNotEmpty;
  bool get hasProfileImage => profileImageUrl != null && profileImageUrl!.isNotEmpty;
  bool get hasPhone => phone != null && phone!.isNotEmpty;
  bool get hasAddress => address != null && address!.isNotEmpty;
  bool get hasSalary => salary != null && salary! > 0;
  
  String get displayName => name.isNotEmpty ? name : email;
  String get displayRole => role ?? 'admin';
  String get displayEmployeeType => employeeType ?? 'admin';
  
  String get formattedSalary {
    if (salary == null) return 'غير محدد';
    return '${salary!.toStringAsFixed(2)} جنيه';
  }
  
  String get formattedHireDate {
    if (hireDate == null) return 'غير محدد';
    return '${hireDate!.day}/${hireDate!.month}/${hireDate!.year}';
  }
  
  String get formattedLastLogin {
    if (lastLogin == null) return 'لم يسجل دخول من قبل';
    final now = DateTime.now();
    final difference = now.difference(lastLogin!);
    
    if (difference.inMinutes < 1) {
      return 'منذ لحظات';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${lastLogin!.day}/${lastLogin!.month}/${lastLogin!.year}';
    }
  }
}
