class SalesInvoiceModel {
  final String id;
  final String invoiceNumber;
  final String? patientId;
  final double totalAmount;
  final double discountPercentage;
  final double discountAmount;
  final double finalAmount;
  final String paymentType;
  final String paymentStatus;
  final double paidAmount;
  final double remainingAmount;
  final DateTime? dueDate;
  final String? notes;
  final String status;
  final String? createdBy;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isCommissionSale;
  final double commissionPercentage;
  final double commissionAmount;
  final double returnAmount;
  final String? returnReason;
  final DateTime? returnedAt;
  final String? returnedBy;
  final String? specialistId;

  SalesInvoiceModel({
    required this.id,
    required this.invoiceNumber,
    this.patientId,
    this.totalAmount = 0.0,
    this.discountPercentage = 0.0,
    this.discountAmount = 0.0,
    this.finalAmount = 0.0,
    required this.paymentType,
    this.paymentStatus = 'pending',
    this.paidAmount = 0.0,
    this.remainingAmount = 0.0,
    this.dueDate,
    this.notes,
    this.status = 'active',
    this.createdBy,
    this.createdAt,
    this.updatedAt,
    this.isCommissionSale = false,
    this.commissionPercentage = 0.0,
    this.commissionAmount = 0.0,
    this.returnAmount = 0.0,
    this.returnReason,
    this.returnedAt,
    this.returnedBy,
    this.specialistId,
  });

  factory SalesInvoiceModel.fromJson(Map<String, dynamic> json) {
    return SalesInvoiceModel(
      id: json['id'] ?? '',
      invoiceNumber: json['invoice_number'] ?? '',
      patientId: json['patient_id'],
      totalAmount: double.tryParse(json['total_amount'].toString()) ?? 0.0,
      discountPercentage: double.tryParse(json['discount_percentage'].toString()) ?? 0.0,
      discountAmount: double.tryParse(json['discount_amount'].toString()) ?? 0.0,
      finalAmount: double.tryParse(json['final_amount'].toString()) ?? 0.0,
      paymentType: json['payment_type'] ?? '',
      paymentStatus: json['payment_status'] ?? 'pending',
      paidAmount: double.tryParse(json['paid_amount'].toString()) ?? 0.0,
      remainingAmount: double.tryParse(json['remaining_amount'].toString()) ?? 0.0,
      dueDate: json['due_date'] != null 
          ? DateTime.tryParse(json['due_date']) 
          : null,
      notes: json['notes'],
      status: json['status'] ?? 'active',
      createdBy: json['created_by'],
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      isCommissionSale: json['is_commission_sale'] ?? false,
      commissionPercentage: double.tryParse(json['commission_percentage'].toString()) ?? 0.0,
      commissionAmount: double.tryParse(json['commission_amount'].toString()) ?? 0.0,
      returnAmount: double.tryParse(json['return_amount'].toString()) ?? 0.0,
      returnReason: json['return_reason'],
      returnedAt: json['returned_at'] != null 
          ? DateTime.tryParse(json['returned_at']) 
          : null,
      returnedBy: json['returned_by'],
      specialistId: json['specialist_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'patient_id': patientId,
      'total_amount': totalAmount,
      'discount_percentage': discountPercentage,
      'discount_amount': discountAmount,
      'final_amount': finalAmount,
      'payment_type': paymentType,
      'payment_status': paymentStatus,
      'paid_amount': paidAmount,
      'remaining_amount': remainingAmount,
      'due_date': dueDate?.toIso8601String().split('T')[0],
      'notes': notes,
      'status': status,
      'created_by': createdBy,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_commission_sale': isCommissionSale,
      'commission_percentage': commissionPercentage,
      'commission_amount': commissionAmount,
      'return_amount': returnAmount,
      'return_reason': returnReason,
      'returned_at': returnedAt?.toIso8601String(),
      'returned_by': returnedBy,
      'specialist_id': specialistId,
    };
  }

  SalesInvoiceModel copyWith({
    String? id,
    String? invoiceNumber,
    String? patientId,
    double? totalAmount,
    double? discountPercentage,
    double? discountAmount,
    double? finalAmount,
    String? paymentType,
    String? paymentStatus,
    double? paidAmount,
    double? remainingAmount,
    DateTime? dueDate,
    String? notes,
    String? status,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isCommissionSale,
    double? commissionPercentage,
    double? commissionAmount,
    double? returnAmount,
    String? returnReason,
    DateTime? returnedAt,
    String? returnedBy,
    String? specialistId,
  }) {
    return SalesInvoiceModel(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      patientId: patientId ?? this.patientId,
      totalAmount: totalAmount ?? this.totalAmount,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      discountAmount: discountAmount ?? this.discountAmount,
      finalAmount: finalAmount ?? this.finalAmount,
      paymentType: paymentType ?? this.paymentType,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paidAmount: paidAmount ?? this.paidAmount,
      remainingAmount: remainingAmount ?? this.remainingAmount,
      dueDate: dueDate ?? this.dueDate,
      notes: notes ?? this.notes,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isCommissionSale: isCommissionSale ?? this.isCommissionSale,
      commissionPercentage: commissionPercentage ?? this.commissionPercentage,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      returnAmount: returnAmount ?? this.returnAmount,
      returnReason: returnReason ?? this.returnReason,
      returnedAt: returnedAt ?? this.returnedAt,
      returnedBy: returnedBy ?? this.returnedBy,
      specialistId: specialistId ?? this.specialistId,
    );
  }

  // Helper methods
  bool get hasPatient => patientId != null && patientId!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  bool get hasDiscount => discountPercentage > 0 || discountAmount > 0;
  bool get hasCommission => isCommissionSale && commissionPercentage > 0;
  bool get hasReturn => returnAmount > 0;
  bool get hasDueDate => dueDate != null;

  String get displayInvoiceNumber => invoiceNumber.isNotEmpty ? invoiceNumber : 'غير محدد';

  // Payment status helpers
  bool get isPending => paymentStatus.toLowerCase() == 'pending';
  bool get isPaid => paymentStatus.toLowerCase() == 'paid';
  bool get isPartiallyPaid => paymentStatus.toLowerCase() == 'partially_paid';
  bool get isOverdue => paymentStatus.toLowerCase() == 'overdue';
  bool get isCancelled => paymentStatus.toLowerCase() == 'cancelled';

  String get paymentStatusText {
    switch (paymentStatus.toLowerCase()) {
      case 'pending':
        return 'في الانتظار';
      case 'paid':
        return 'مدفوع';
      case 'partially_paid':
        return 'مدفوع جزئياً';
      case 'overdue':
        return 'متأخر';
      case 'cancelled':
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  // Payment type helpers
  bool get isCashPayment => paymentType.toLowerCase() == 'cash';
  bool get isCardPayment => paymentType.toLowerCase() == 'card';
  bool get isBankTransfer => paymentType.toLowerCase() == 'bank_transfer';
  bool get isInstallment => paymentType.toLowerCase() == 'installment';

  String get paymentTypeText {
    switch (paymentType.toLowerCase()) {
      case 'cash':
        return 'نقدي';
      case 'card':
        return 'بطاقة';
      case 'bank_transfer':
        return 'تحويل بنكي';
      case 'installment':
        return 'تقسيط';
      default:
        return 'غير محدد';
    }
  }

  // Status helpers
  bool get isActive => status.toLowerCase() == 'active';
  bool get isReturned => status.toLowerCase() == 'returned';
  bool get isVoided => status.toLowerCase() == 'voided';

  String get statusText {
    switch (status.toLowerCase()) {
      case 'active':
        return 'نشط';
      case 'returned':
        return 'مرتجع';
      case 'voided':
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  // Formatted amounts
  String get formattedTotalAmount => '${totalAmount.toStringAsFixed(2)} جنيه';
  String get formattedFinalAmount => '${finalAmount.toStringAsFixed(2)} جنيه';
  String get formattedPaidAmount => '${paidAmount.toStringAsFixed(2)} جنيه';
  String get formattedRemainingAmount => '${remainingAmount.toStringAsFixed(2)} جنيه';
  String get formattedDiscountAmount => '${discountAmount.toStringAsFixed(2)} جنيه';
  String get formattedCommissionAmount => '${commissionAmount.toStringAsFixed(2)} جنيه';
  String get formattedReturnAmount => '${returnAmount.toStringAsFixed(2)} جنيه';

  String get formattedDiscountPercentage => '${discountPercentage.toStringAsFixed(1)}%';
  String get formattedCommissionPercentage => '${commissionPercentage.toStringAsFixed(1)}%';

  // Date formatting
  String get formattedCreatedDate {
    if (createdAt == null) return 'غير محدد';
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }

  String get formattedDueDate {
    if (dueDate == null) return 'غير محدد';
    return '${dueDate!.day}/${dueDate!.month}/${dueDate!.year}';
  }

  String get formattedReturnedDate {
    if (returnedAt == null) return 'غير محدد';
    return '${returnedAt!.day}/${returnedAt!.month}/${returnedAt!.year}';
  }

  // Due date helpers
  bool get isDueToday {
    if (dueDate == null) return false;
    final now = DateTime.now();
    return dueDate!.year == now.year &&
           dueDate!.month == now.month &&
           dueDate!.day == now.day;
  }

  bool get isDuePast => dueDate != null && dueDate!.isBefore(DateTime.now());
  bool get isDueFuture => dueDate != null && dueDate!.isAfter(DateTime.now());

  int get daysUntilDue {
    if (dueDate == null) return 0;
    return dueDate!.difference(DateTime.now()).inDays;
  }

  String get dueStatus {
    if (dueDate == null) return 'بدون تاريخ استحقاق';
    if (isDueToday) return 'مستحق اليوم';
    if (isDuePast) {
      final days = DateTime.now().difference(dueDate!).inDays;
      return 'متأخر $days ${days == 1 ? 'يوم' : 'أيام'}';
    } else {
      final days = daysUntilDue;
      return 'مستحق خلال $days ${days == 1 ? 'يوم' : 'أيام'}';
    }
  }

  @override
  String toString() {
    return 'SalesInvoiceModel(id: $id, number: $invoiceNumber, amount: $formattedFinalAmount, status: $paymentStatusText)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SalesInvoiceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
