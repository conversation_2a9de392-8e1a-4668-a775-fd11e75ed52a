class ExaminationModel {
  final String id;
  final String patientId;
  final String title;
  final String type;
  final String? description;
  final String? imageUrl;
  final DateTime examinationDate;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ExaminationModel({
    required this.id,
    required this.patientId,
    required this.title,
    required this.type,
    this.description,
    this.imageUrl,
    required this.examinationDate,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  factory ExaminationModel.fromJson(Map<String, dynamic> json) {
    return ExaminationModel(
      id: json['id'] ?? '',
      patientId: json['patient_id'] ?? '',
      title: json['title'] ?? '',
      type: json['type'] ?? '',
      description: json['description'],
      imageUrl: json['image_url'],
      examinationDate:
          json['examination_date'] != null
              ? DateTime.parse(json['examination_date'])
              : DateTime.now(),
      notes: json['notes'],
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'title': title,
      'type': type,
      'description': description,
      'image_url': imageUrl,
      'examination_date': examinationDate.toIso8601String().split('T')[0],
      'notes': notes,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  ExaminationModel copyWith({
    String? id,
    String? patientId,
    String? title,
    String? type,
    String? description,
    String? imageUrl,
    DateTime? examinationDate,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExaminationModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      title: title ?? this.title,
      type: type ?? this.type,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      examinationDate: examinationDate ?? this.examinationDate,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get hasDescription => description != null && description!.isNotEmpty;
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;

  String get displayTitle => title.isNotEmpty ? title : 'فحص غير محدد';
  String get displayType => type.isNotEmpty ? type : 'نوع غير محدد';

  /// الحصول على نوع الفحص بالعربية
  String get displayTypeArabic => _getExaminationTypeArabic(type);

  /// ترجمة نوع الفحص إلى العربية
  String _getExaminationTypeArabic(String type) {
    switch (type.toLowerCase()) {
      case 'x-ray':
      case 'xray':
      case 'x_ray':
        return 'أشعة سينية';
      case 'blood':
      case 'blood_test':
      case 'blood test':
        return 'تحليل دم';
      case 'ultrasound':
      case 'ultra_sound':
      case 'ultra sound':
        return 'موجات صوتية';
      case 'mri':
      case 'magnetic resonance':
        return 'رنين مغناطيسي';
      case 'ct':
      case 'ct_scan':
      case 'ct scan':
      case 'computed tomography':
        return 'أشعة مقطعية';
      case 'ecg':
      case 'ekg':
      case 'electrocardiogram':
        return 'رسم قلب';
      case 'eeg':
      case 'electroencephalogram':
        return 'رسم مخ';
      case 'hearing_test':
      case 'hearing test':
      case 'audiometry':
        return 'فحص السمع';
      case 'vision_test':
      case 'vision test':
      case 'eye_test':
      case 'eye test':
        return 'فحص النظر';
      case 'urine':
      case 'urine_test':
      case 'urine test':
      case 'urinalysis':
        return 'تحليل بول';
      case 'stool':
      case 'stool_test':
      case 'stool test':
        return 'تحليل براز';
      case 'biopsy':
        return 'خزعة';
      case 'endoscopy':
        return 'منظار';
      case 'colonoscopy':
        return 'منظار القولون';
      case 'mammography':
      case 'mammogram':
        return 'أشعة الثدي';
      case 'bone_density':
      case 'bone density':
      case 'dexa':
      case 'dexa_scan':
        return 'كثافة العظام';
      case 'stress_test':
      case 'stress test':
      case 'exercise_test':
        return 'اختبار الإجهاد';
      case 'allergy_test':
      case 'allergy test':
      case 'skin_test':
        return 'اختبار الحساسية';
      case 'genetic_test':
      case 'genetic test':
      case 'dna_test':
        return 'فحص جيني';
      case 'lab_test':
      case 'lab test':
      case 'laboratory':
        return 'فحص مختبري';
      case 'clinical_exam':
      case 'clinical exam':
      case 'clinical':
        return 'فحص إكلينيكي';
      case 'physical_exam':
      case 'physical exam':
      case 'physical':
        return 'فحص جسدي';
      case 'neurological':
      case 'neuro':
      case 'neurological_exam':
        return 'فحص عصبي';
      case 'cardiac':
      case 'heart':
      case 'cardiac_exam':
        return 'فحص قلبي';
      case 'respiratory':
      case 'lung':
      case 'pulmonary':
        return 'فحص تنفسي';
      case 'dermatological':
      case 'skin':
      case 'dermatology':
        return 'فحص جلدي';
      case 'ophthalmological':
      case 'eye':
      case 'ophthalmology':
        return 'فحص عيون';
      case 'dental':
      case 'teeth':
      case 'oral':
        return 'فحص أسنان';
      case 'orthopedic':
      case 'bone':
      case 'joint':
      case 'musculoskeletal':
        return 'فحص عظام';
      case 'psychiatric':
      case 'mental':
      case 'psychology':
        return 'فحص نفسي';
      case 'gynecological':
      case 'gynecology':
      case 'women':
        return 'فحص نسائي';
      case 'pediatric':
      case 'pediatrics':
      case 'child':
      case 'children':
        return 'فحص أطفال';
      case 'geriatric':
      case 'elderly':
      case 'senior':
        return 'فحص مسنين';
      case 'general':
      case 'general_exam':
      case 'checkup':
      case 'routine':
        return 'فحص عام';
      case 'consultation':
      case 'consult':
        return 'استشارة';
      case 'follow_up':
      case 'follow up':
      case 'followup':
        return 'متابعة';
      case 'assessment':
      case 'evaluation':
        return 'تقييم';
      case 'screening':
        return 'فحص وقائي';
      case 'diagnosis':
      case 'diagnostic':
        return 'تشخيص';
      case 'behavioral':
      case 'behavior':
        return 'فحص سلوكي';
      case 'cognitive':
        return 'فحص معرفي';
      case 'communication':
        return 'فحص التواصل';
      case 'developmental':
        return 'فحص تطويري';
      case 'hearing':
        return 'فحص السمع';
      case 'motor':
        return 'فحص حركي';
      case 'sensory':
        return 'فحص حسي';
      case 'speech':
        return 'فحص النطق';
      case 'other':
        return 'فحص آخر';
      default:
        return type.isNotEmpty ? type : 'نوع غير محدد';
    }
  }

  String get formattedExaminationDate {
    return '${examinationDate.day}/${examinationDate.month}/${examinationDate.year}';
  }

  String get formattedCreatedDate {
    if (createdAt == null) return 'غير محدد';
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }

  // Examination type helpers
  bool get isXRay =>
      type.toLowerCase().contains('x-ray') ||
      type.toLowerCase().contains('أشعة');
  bool get isBloodTest =>
      type.toLowerCase().contains('blood') || type.toLowerCase().contains('دم');
  bool get isUltrasound =>
      type.toLowerCase().contains('ultrasound') ||
      type.toLowerCase().contains('موجات');
  bool get isMRI =>
      type.toLowerCase().contains('mri') || type.toLowerCase().contains('رنين');
  bool get isCTScan =>
      type.toLowerCase().contains('ct') ||
      type.toLowerCase().contains('مقطعية');

  // Date helpers
  bool get isToday {
    final now = DateTime.now();
    return examinationDate.year == now.year &&
        examinationDate.month == now.month &&
        examinationDate.day == now.day;
  }

  bool get isPast => examinationDate.isBefore(DateTime.now());
  bool get isFuture => examinationDate.isAfter(DateTime.now());

  int get daysFromNow {
    final now = DateTime.now();
    return examinationDate.difference(now).inDays;
  }

  String get timeFromNow {
    if (isToday) return 'اليوم';
    if (isPast) {
      final days = DateTime.now().difference(examinationDate).inDays;
      return 'منذ $days ${days == 1 ? 'يوم' : 'أيام'}';
    } else {
      final days = daysFromNow;
      return 'خلال $days ${days == 1 ? 'يوم' : 'أيام'}';
    }
  }

  @override
  String toString() {
    return 'ExaminationModel(id: $id, title: $title, type: $type, date: $formattedExaminationDate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExaminationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
