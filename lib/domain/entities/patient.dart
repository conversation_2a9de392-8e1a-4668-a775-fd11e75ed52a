import 'package:equatable/equatable.dart';

/// كيان المريض
class Patient extends Equatable {
  final String id;
  final String? authId; // معرف المصادقة
  final String name;
  final String? email;
  final String? phone;
  final int? age;
  final String? gender;
  final double? height;
  final double? weight;
  final bool isPremium;
  final String? medicalConditions;
  final String? allergies;
  final String? medications;
  final String? supplements;
  final String? physicalActivity;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? birthDate;

  const Patient({
    required this.id,
    this.authId,
    required this.name,
    this.email,
    this.phone,
    this.age,
    this.gender,
    this.height,
    this.weight,
    this.isPremium = false,
    this.medicalConditions,
    this.allergies,
    this.medications,
    this.supplements,
    this.physicalActivity,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.birthDate,
  });

  @override
  List<Object?> get props => [
    id,
    authId,
    name,
    email,
    phone,
    age,
    gender,
    height,
    weight,
    isPremium,
    medicalConditions,
    allergies,
    medications,
    supplements,
    physicalActivity,
    notes,
    createdAt,
    updatedAt,
    birthDate,
  ];

  /// حساب العمر من تاريخ الميلاد
  int? get calculatedAge {
    if (birthDate == null) return age;

    final now = DateTime.now();
    int calculatedAge = now.year - birthDate!.year;
    if (now.month < birthDate!.month ||
        (now.month == birthDate!.month && now.day < birthDate!.day)) {
      calculatedAge--;
    }
    return calculatedAge;
  }

  /// حساب مؤشر كتلة الجسم
  double? get bmi {
    if (weight == null || height == null) return null;
    final heightInMeters = height! / 100;
    return weight! / (heightInMeters * heightInMeters);
  }

  /// تصنيف مؤشر كتلة الجسم
  String? get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return null;

    if (bmiValue < 18.5) {
      return 'نقص في الوزن';
    } else if (bmiValue < 25) {
      return 'وزن طبيعي';
    } else if (bmiValue < 30) {
      return 'زيادة في الوزن';
    } else {
      return 'سمنة';
    }
  }

  /// التحقق من اكتمال البيانات الأساسية
  bool get hasBasicInfo {
    return name.isNotEmpty && email != null && phone != null;
  }

  /// التحقق من اكتمال البيانات الطبية
  bool get hasMedicalInfo {
    return height != null && weight != null && birthDate != null;
  }

  /// التحقق من وجود حساسية
  bool get hasAllergies {
    return allergies != null && allergies!.isNotEmpty;
  }

  /// التحقق من وجود أدوية
  bool get hasMedications {
    return medications != null && medications!.isNotEmpty;
  }

  /// التحقق من وجود مكملات غذائية
  bool get hasSupplements {
    return supplements != null && supplements!.isNotEmpty;
  }

  /// التحقق من وجود حالات طبية
  bool get hasMedicalConditions {
    return medicalConditions != null && medicalConditions!.isNotEmpty;
  }

  /// إنشاء Patient من JSON
  factory Patient.fromJson(Map<String, dynamic> json) {
    return Patient(
      id: json['id']?.toString() ?? '',
      authId: json['auth_id']?.toString(),
      name: json['name']?.toString() ?? '',
      email: json['email']?.toString(),
      phone: json['phone']?.toString(),
      age: json['age'] != null ? int.tryParse(json['age'].toString()) : null,
      gender: json['gender']?.toString(),
      height:
          json['height'] != null
              ? double.tryParse(json['height'].toString())
              : null,
      weight:
          json['weight'] != null
              ? double.tryParse(json['weight'].toString())
              : null,
      isPremium: json['is_premium'] == true,
      medicalConditions: json['medical_conditions']?.toString(),
      allergies: json['allergies']?.toString(),
      medications: json['medications']?.toString(),
      supplements: json['supplements']?.toString(),
      physicalActivity: json['physical_activity']?.toString(),
      notes: json['notes']?.toString(),
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'].toString())
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'].toString())
              : null,
      birthDate:
          json['birth_date'] != null
              ? DateTime.tryParse(json['birth_date'].toString())
              : null,
    );
  }

  /// تحويل Patient إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'auth_id': authId,
      'name': name,
      'email': email,
      'phone': phone,
      'age': age,
      'gender': gender,
      'height': height,
      'weight': weight,
      'is_premium': isPremium,
      'medical_conditions': medicalConditions,
      'allergies': allergies,
      'medications': medications,
      'supplements': supplements,
      'physical_activity': physicalActivity,
      'notes': notes,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'birth_date': birthDate?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من Patient
  Patient copyWith({
    String? id,
    String? authId,
    String? name,
    String? email,
    String? phone,
    int? age,
    String? gender,
    double? height,
    double? weight,
    bool? isPremium,
    String? medicalConditions,
    String? allergies,
    String? medications,
    String? supplements,
    String? physicalActivity,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? birthDate,
  }) {
    return Patient(
      id: id ?? this.id,
      authId: authId ?? this.authId,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      isPremium: isPremium ?? this.isPremium,
      medicalConditions: medicalConditions ?? this.medicalConditions,
      allergies: allergies ?? this.allergies,
      medications: medications ?? this.medications,
      supplements: supplements ?? this.supplements,
      physicalActivity: physicalActivity ?? this.physicalActivity,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      birthDate: birthDate ?? this.birthDate,
    );
  }
}
