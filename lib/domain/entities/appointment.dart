import 'package:equatable/equatable.dart';

/// كيان الموعد
class Appointment extends Equatable {
  final String id;
  final String? patientId;
  final DateTime appointmentDate;
  final String status;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? timeSlotId;

  const Appointment({
    required this.id,
    this.patientId,
    required this.appointmentDate,
    required this.status,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.timeSlotId,
  });

  @override
  List<Object?> get props => [
        id,
        patientId,
        appointmentDate,
        status,
        notes,
        createdAt,
        updatedAt,
        timeSlotId,
      ];

  /// التحقق من إمكانية الحجز
  bool get isAvailable => status.toLowerCase() == 'available';

  /// التحقق من كون الموعد محجوز
  bool get isBooked => status.toLowerCase() == 'booked';

  /// التحقق من كون الموعد مكتمل
  bool get isCompleted => status.toLowerCase() == 'completed';

  /// التحقق من كون الموعد ملغي
  bool get isCancelled => status.toLowerCase() == 'cancelled';

  /// التحقق من كون الموعد في الماضي
  bool get isPast {
    final now = DateTime.now();
    return appointmentDate.isBefore(now);
  }

  /// التحقق من كون الموعد اليوم
  bool get isToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final appointmentDay = DateTime(
      appointmentDate.year,
      appointmentDate.month,
      appointmentDate.day,
    );
    return appointmentDay.isAtSameMomentAs(today);
  }

  /// التحقق من كون الموعد غداً
  bool get isTomorrow {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final appointmentDay = DateTime(
      appointmentDate.year,
      appointmentDate.month,
      appointmentDate.day,
    );
    return appointmentDay.isAtSameMomentAs(tomorrow);
  }

  /// الحصول على الوقت المتبقي للموعد
  Duration? get timeUntilAppointment {
    if (isPast) return null;
    return appointmentDate.difference(DateTime.now());
  }

  /// التحقق من إمكانية الإلغاء
  bool get canBeCancelled {
    return !isPast && !isCompleted && !isCancelled;
  }

  /// التحقق من إمكانية التعديل
  bool get canBeModified {
    return !isPast && !isCompleted && !isCancelled;
  }

  /// إنشاء Appointment من JSON
  factory Appointment.fromJson(Map<String, dynamic> json) {
    return Appointment(
      id: json['id']?.toString() ?? '',
      patientId: json['patient_id']?.toString(),
      appointmentDate: json['appointment_date'] != null
          ? DateTime.parse(json['appointment_date'].toString())
          : DateTime.now(),
      status: json['status']?.toString() ?? 'scheduled',
      notes: json['notes']?.toString(),
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'].toString())
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'].toString())
          : null,
      timeSlotId: json['time_slot_id']?.toString() ?? json['time_slot']?.toString(),
    );
  }

  /// تحويل Appointment إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'appointment_date': appointmentDate.toIso8601String(),
      'status': status,
      'notes': notes,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'time_slot_id': timeSlotId,
      'time_slot': timeSlotId,
    };
  }

  /// إنشاء نسخة محدثة من Appointment
  Appointment copyWith({
    String? id,
    String? patientId,
    DateTime? appointmentDate,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? timeSlotId,
  }) {
    return Appointment(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      timeSlotId: timeSlotId ?? this.timeSlotId,
    );
  }

  /// تنسيق تاريخ الموعد
  String get formattedDate {
    return '${appointmentDate.day}/${appointmentDate.month}/${appointmentDate.year}';
  }

  /// تنسيق وقت الموعد
  String get formattedTime {
    return '${appointmentDate.hour.toString().padLeft(2, '0')}:${appointmentDate.minute.toString().padLeft(2, '0')}';
  }

  /// تنسيق تاريخ ووقت الموعد
  String get formattedDateTime {
    return '$formattedDate - $formattedTime';
  }

  /// الحصول على لون الحالة
  String get statusColor {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return '#2196F3'; // أزرق
      case 'completed':
        return '#4CAF50'; // أخضر
      case 'cancelled':
        return '#F44336'; // أحمر
      case 'confirmed':
        return '#FF9800'; // برتقالي
      default:
        return '#607D8B'; // رمادي
    }
  }

  /// الحصول على نص الحالة بالعربية
  String get statusText {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return 'مجدول';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'confirmed':
        return 'مؤكد';
      default:
        return 'غير محدد';
    }
  }
}
