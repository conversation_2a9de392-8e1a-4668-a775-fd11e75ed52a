import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';

/// شريط تطبيق مخصص
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final PreferredSizeWidget? bottom;
  final bool automaticallyImplyLeading;
  final bool showBackButton;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.bottom,
    this.automaticallyImplyLeading = true,
    this.showBackButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: foregroundColor ?? AppColors.textPrimary,
        ),
      ),
      actions: actions,
      leading:
          leading ??
          (showBackButton
              ? IconButton(
                icon: const Icon(Icons.arrow_back_ios),
                onPressed: () => Navigator.of(context).pop(),
              )
              : null),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? AppColors.white,
      foregroundColor: foregroundColor ?? AppColors.textPrimary,
      elevation: elevation ?? 0,
      bottom: bottom,
      automaticallyImplyLeading: automaticallyImplyLeading && !showBackButton,
      shadowColor: AppColors.shadow,
    );
  }

  @override
  Size get preferredSize =>
      Size.fromHeight(kToolbarHeight + (bottom?.preferredSize.height ?? 0));
}

/// شريط تطبيق بسيط
class SimpleAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onBackPressed;

  const SimpleAppBar({super.key, required this.title, this.onBackPressed});

  @override
  Widget build(BuildContext context) {
    return CustomAppBar(
      title: title,
      leading:
          onBackPressed != null
              ? IconButton(
                icon: const Icon(Icons.arrow_back_ios),
                onPressed: onBackPressed,
              )
              : null,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// شريط تطبيق مع بحث
class SearchAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String? searchHint;
  final void Function(String)? onSearchChanged;
  final VoidCallback? onSearchClear;

  const SearchAppBar({
    super.key,
    required this.title,
    this.searchHint,
    this.onSearchChanged,
    this.onSearchClear,
  });

  @override
  Widget build(BuildContext context) {
    return CustomAppBar(
      title: title,
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            showSearch(
              context: context,
              delegate: CustomSearchDelegate(
                searchHint: searchHint,
                onSearchChanged: onSearchChanged,
                onSearchClear: onSearchClear,
              ),
            );
          },
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// مفوض البحث المخصص
class CustomSearchDelegate extends SearchDelegate<String> {
  final String? searchHint;
  final void Function(String)? onSearchChanged;
  final VoidCallback? onSearchClear;

  CustomSearchDelegate({
    this.searchHint,
    this.onSearchChanged,
    this.onSearchClear,
  });

  @override
  String get searchFieldLabel => searchHint ?? 'بحث...';

  @override
  TextStyle get searchFieldStyle => TextStyle(
    fontFamily: 'Cairo',
    fontSize: 16.sp,
    color: AppColors.textPrimary,
  );

  @override
  ThemeData appBarTheme(BuildContext context) {
    return Theme.of(context).copyWith(
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.white,
        foregroundColor: AppColors.textPrimary,
        elevation: 0,
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: InputBorder.none,
        hintStyle: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 16.sp,
          color: AppColors.textLight,
        ),
      ),
    );
  }

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      if (query.isNotEmpty)
        IconButton(
          icon: const Icon(Icons.clear),
          onPressed: () {
            query = '';
            onSearchClear?.call();
          },
        ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, '');
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    onSearchChanged?.call(query);
    return Container();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isNotEmpty) {
      onSearchChanged?.call(query);
    }
    return Container();
  }
}
