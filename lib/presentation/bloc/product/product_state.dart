part of 'product_bloc.dart';

/// حالات المنتجات
abstract class ProductState extends Equatable {
  const ProductState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class ProductInitial extends ProductState {}

/// حالة التحميل
class ProductLoading extends ProductState {}

/// حالة تحميل المنتجات بنجاح
class ProductLoaded extends ProductState {
  final List<Product> products;

  const ProductLoaded({required this.products});

  @override
  List<Object?> get props => [products];
}

/// حالة تحميل المنتجات المميزة
class FeaturedProductsLoaded extends ProductState {
  final List<Product> products;

  const FeaturedProductsLoaded({required this.products});

  @override
  List<Object?> get props => [products];
}

/// حالة تحميل المنتجات حسب الفئة
class ProductsByCategoryLoaded extends ProductState {
  final String category;
  final List<Product> products;

  const ProductsByCategoryLoaded({
    required this.category,
    required this.products,
  });

  @override
  List<Object?> get props => [category, products];
}

/// حالة تحميل فئات المنتجات
class ProductCategoriesLoaded extends ProductState {
  final List<String> categories;

  const ProductCategoriesLoaded({required this.categories});

  @override
  List<Object?> get props => [categories];
}

/// حالة نتائج البحث
class ProductSearchResults extends ProductState {
  final List<Product> products;

  const ProductSearchResults({required this.products});

  @override
  List<Object?> get props => [products];
}

/// حالة تحميل تفاصيل المنتج
class ProductDetailLoaded extends ProductState {
  final Product product;

  const ProductDetailLoaded({required this.product});

  @override
  List<Object?> get props => [product];
}

/// حالة تحميل الإحصائيات
class ProductStatsLoaded extends ProductState {
  final Map<String, dynamic> stats;

  const ProductStatsLoaded({required this.stats});

  @override
  List<Object?> get props => [stats];
}

/// حالة الخطأ
class ProductError extends ProductState {
  final Failure failure;

  const ProductError({required this.failure});

  @override
  List<Object?> get props => [failure];
}
