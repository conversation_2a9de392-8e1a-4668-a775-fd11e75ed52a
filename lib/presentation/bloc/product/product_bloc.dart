import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/product.dart';
import '../../../core/errors/failures.dart';
import '../../../data/repositories/product_repository.dart';

part 'product_event.dart';
part 'product_state.dart';

/// BLoC للمنتجات
class ProductBloc extends Bloc<ProductEvent, ProductState> {
  final ProductRepository _productRepository;

  ProductBloc({ProductRepository? productRepository})
      : _productRepository = productRepository ?? ProductRepository(),
        super(ProductInitial()) {
    on<LoadProducts>(_onLoadProducts);
    on<LoadFeaturedProducts>(_onLoadFeaturedProducts);
    on<LoadProductsByCategory>(_onLoadProductsByCategory);
    on<LoadProductCategories>(_onLoadProductCategories);
    on<SearchProducts>(_onSearchProducts);
    on<LoadProductById>(_onLoadProductById);
    on<LoadProductStats>(_onLoadProductStats);
    on<RefreshProducts>(_onRefreshProducts);
  }

  /// تحميل المنتجات
  Future<void> _onLoadProducts(
    LoadProducts event,
    Emitter<ProductState> emit,
  ) async {
    emit(ProductLoading());

    try {
      final products = await _productRepository.getAllProducts(
        category: event.category,
        isAvailable: event.isAvailable,
        searchTerm: event.searchTerm,
        limit: event.limit,
        offset: event.offset,
        sortBy: event.sortBy,
        ascending: event.ascending,
      );

      emit(ProductLoaded(products: products));
    } catch (e) {
      emit(ProductError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل المنتجات المميزة
  Future<void> _onLoadFeaturedProducts(
    LoadFeaturedProducts event,
    Emitter<ProductState> emit,
  ) async {
    emit(ProductLoading());

    try {
      final products = await _productRepository.getFeaturedProducts(
        limit: event.limit,
      );

      emit(FeaturedProductsLoaded(products: products));
    } catch (e) {
      emit(ProductError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل المنتجات حسب الفئة
  Future<void> _onLoadProductsByCategory(
    LoadProductsByCategory event,
    Emitter<ProductState> emit,
  ) async {
    emit(ProductLoading());

    try {
      final products = await _productRepository.getProductsByCategory(
        event.category,
        limit: event.limit,
      );

      emit(ProductsByCategoryLoaded(
        category: event.category,
        products: products,
      ));
    } catch (e) {
      emit(ProductError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل فئات المنتجات
  Future<void> _onLoadProductCategories(
    LoadProductCategories event,
    Emitter<ProductState> emit,
  ) async {
    emit(ProductLoading());

    try {
      final categories = await _productRepository.getAvailableCategories();

      emit(ProductCategoriesLoaded(categories: categories));
    } catch (e) {
      emit(ProductError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// البحث في المنتجات
  Future<void> _onSearchProducts(
    SearchProducts event,
    Emitter<ProductState> emit,
  ) async {
    emit(ProductLoading());

    try {
      final products = await _productRepository.searchProducts(
        searchTerm: event.searchTerm,
        category: event.category,
        minPrice: event.minPrice,
        maxPrice: event.maxPrice,
        limit: event.limit,
      );

      emit(ProductSearchResults(products: products));
    } catch (e) {
      emit(ProductError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل منتج بواسطة ID
  Future<void> _onLoadProductById(
    LoadProductById event,
    Emitter<ProductState> emit,
  ) async {
    emit(ProductLoading());

    try {
      final product = await _productRepository.getProductById(event.productId);

      if (product != null) {
        emit(ProductDetailLoaded(product: product));
      } else {
        emit(ProductError(
          failure: ServerFailure(message: 'المنتج غير موجود'),
        ));
      }
    } catch (e) {
      emit(ProductError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل إحصائيات المنتجات
  Future<void> _onLoadProductStats(
    LoadProductStats event,
    Emitter<ProductState> emit,
  ) async {
    emit(ProductLoading());

    try {
      final stats = await _productRepository.getProductStats();

      emit(ProductStatsLoaded(stats: stats));
    } catch (e) {
      emit(ProductError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحديث المنتجات
  Future<void> _onRefreshProducts(
    RefreshProducts event,
    Emitter<ProductState> emit,
  ) async {
    try {
      final products = await _productRepository.getAllProducts(
        category: event.category,
        isAvailable: event.isAvailable,
        searchTerm: event.searchTerm,
        limit: event.limit,
        offset: event.offset,
        sortBy: event.sortBy,
        ascending: event.ascending,
      );

      emit(ProductLoaded(products: products));
    } catch (e) {
      emit(ProductError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }
}


