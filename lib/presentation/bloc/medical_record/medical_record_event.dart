import 'package:equatable/equatable.dart';

abstract class MedicalRecordEvent extends Equatable {
  const MedicalRecordEvent();

  @override
  List<Object?> get props => [];
}

class LoadMedicalRecord extends MedicalRecordEvent {
  final String patientId;

  const LoadMedicalRecord(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadMedicalRecordByAuthId extends MedicalRecordEvent {
  final String authId;

  const LoadMedicalRecordByAuthId(this.authId);

  @override
  List<Object?> get props => [authId];
}

class RefreshMedicalRecord extends MedicalRecordEvent {
  final String patientId;

  const RefreshMedicalRecord(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadPatientInfo extends MedicalRecordEvent {
  final String patientId;

  const LoadPatientInfo(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadExaminations extends MedicalRecordEvent {
  final String patientId;

  const LoadExaminations(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadHomework extends MedicalRecordEvent {
  final String patientId;

  const LoadHomework(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class ChangeTab extends MedicalRecordEvent {
  final int tabIndex;

  const ChangeTab(this.tabIndex);

  @override
  List<Object?> get props => [tabIndex];
}

class ClearMedicalRecord extends MedicalRecordEvent {
  const ClearMedicalRecord();
}