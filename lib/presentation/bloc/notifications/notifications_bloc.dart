import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../core/services/notification_service.dart';
import '../../../core/services/fcm_integration_service.dart';
import '../../../core/utils/app_logger.dart';

part 'notifications_event.dart';
part 'notifications_state.dart';

/// BLoC للإشعارات
class NotificationsBloc extends Bloc<NotificationsEvent, NotificationsState> {
  final NotificationService _notificationService;
  final FCMIntegrationService _fcmService;

  NotificationsBloc({
    NotificationService? notificationService,
    FCMIntegrationService? fcmService,
  })  : _notificationService = notificationService ?? NotificationService(),
        _fcmService = fcmService ?? FCMIntegrationService(),
        super(NotificationsInitial()) {
    on<InitializeNotifications>(_onInitializeNotifications);
    on<RequestNotificationPermissions>(_onRequestNotificationPermissions);
    on<ShowNotification>(_onShowNotification);
    on<ScheduleNotification>(_onScheduleNotification);
    on<CancelNotification>(_onCancelNotification);
    on<CancelAllNotifications>(_onCancelAllNotifications);
    on<UpdateNotificationSettings>(_onUpdateNotificationSettings);
    on<SubscribeToTopic>(_onSubscribeToTopic);
    on<UnsubscribeFromTopic>(_onUnsubscribeFromTopic);
  }

  /// تهيئة خدمات الإشعارات
  Future<void> _onInitializeNotifications(
    InitializeNotifications event,
    Emitter<NotificationsState> emit,
  ) async {
    emit(NotificationsLoading());

    try {
      AppLogger.info(
        '🔔 Initializing notifications',
        category: LogCategory.general,
      );

      // تهيئة خدمة الإشعارات المحلية
      await _notificationService.initialize();

      // تهيئة خدمة FCM
      await _fcmService.initialize();

      emit(NotificationsInitialized(
        fcmToken: _fcmService.fcmToken,
      ));

      AppLogger.info(
        '✅ Notifications initialized successfully',
        category: LogCategory.general,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to initialize notifications',
        category: LogCategory.general,
        error: e,
      );

      emit(NotificationsError(
        message: 'فشل في تهيئة الإشعارات: ${e.toString()}',
      ));
    }
  }

  /// طلب أذونات الإشعارات
  Future<void> _onRequestNotificationPermissions(
    RequestNotificationPermissions event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      AppLogger.info(
        '🔐 Requesting notification permissions',
        category: LogCategory.general,
      );

      final granted = await _notificationService.requestPermissions();

      emit(NotificationPermissionsResult(granted: granted));

      AppLogger.info(
        '✅ Notification permissions result',
        category: LogCategory.general,
        data: {'granted': granted.toString()},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to request notification permissions',
        category: LogCategory.general,
        error: e,
      );

      emit(NotificationsError(
        message: 'فشل في طلب أذونات الإشعارات: ${e.toString()}',
      ));
    }
  }

  /// عرض إشعار فوري
  Future<void> _onShowNotification(
    ShowNotification event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      AppLogger.info(
        '📱 Showing notification',
        category: LogCategory.general,
        data: {
          'id': event.id.toString(),
          'title': event.title,
        },
      );

      await _notificationService.showNotification(
        id: event.id,
        title: event.title,
        body: event.body,
        payload: event.payload,
      );

      emit(NotificationShown(id: event.id));
    } catch (e) {
      AppLogger.error(
        '❌ Failed to show notification',
        category: LogCategory.general,
        error: e,
      );

      emit(NotificationsError(
        message: 'فشل في عرض الإشعار: ${e.toString()}',
      ));
    }
  }

  /// جدولة إشعار
  Future<void> _onScheduleNotification(
    ScheduleNotification event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      AppLogger.info(
        '⏰ Scheduling notification',
        category: LogCategory.general,
        data: {
          'id': event.id.toString(),
          'title': event.title,
          'scheduledDate': event.scheduledDate.toIso8601String(),
        },
      );

      await _notificationService.scheduleNotification(
        id: event.id,
        title: event.title,
        body: event.body,
        scheduledDate: event.scheduledDate,
        payload: event.payload,
      );

      emit(NotificationScheduled(
        id: event.id,
        scheduledDate: event.scheduledDate,
      ));
    } catch (e) {
      AppLogger.error(
        '❌ Failed to schedule notification',
        category: LogCategory.general,
        error: e,
      );

      emit(NotificationsError(
        message: 'فشل في جدولة الإشعار: ${e.toString()}',
      ));
    }
  }

  /// إلغاء إشعار
  Future<void> _onCancelNotification(
    CancelNotification event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      AppLogger.info(
        '🗑️ Cancelling notification',
        category: LogCategory.general,
        data: {'id': event.id.toString()},
      );

      await _notificationService.cancelNotification(event.id);

      emit(NotificationCancelled(id: event.id));
    } catch (e) {
      AppLogger.error(
        '❌ Failed to cancel notification',
        category: LogCategory.general,
        error: e,
      );

      emit(NotificationsError(
        message: 'فشل في إلغاء الإشعار: ${e.toString()}',
      ));
    }
  }

  /// إلغاء جميع الإشعارات
  Future<void> _onCancelAllNotifications(
    CancelAllNotifications event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      AppLogger.info(
        '🗑️ Cancelling all notifications',
        category: LogCategory.general,
      );

      await _notificationService.cancelAllNotifications();

      emit(AllNotificationsCancelled());
    } catch (e) {
      AppLogger.error(
        '❌ Failed to cancel all notifications',
        category: LogCategory.general,
        error: e,
      );

      emit(NotificationsError(
        message: 'فشل في إلغاء جميع الإشعارات: ${e.toString()}',
      ));
    }
  }

  /// تحديث إعدادات الإشعارات
  Future<void> _onUpdateNotificationSettings(
    UpdateNotificationSettings event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      AppLogger.info(
        '⚙️ Updating notification settings',
        category: LogCategory.general,
        data: {
          'appointments': event.enableAppointmentReminders.toString(),
          'homework': event.enableHomeworkNotifications.toString(),
          'examinations': event.enableExaminationResults.toString(),
        },
      );

      await _fcmService.updateNotificationSettings(
        enableAppointmentReminders: event.enableAppointmentReminders,
        enableHomeworkNotifications: event.enableHomeworkNotifications,
        enableExaminationResults: event.enableExaminationResults,
      );

      emit(NotificationSettingsUpdated(
        enableAppointmentReminders: event.enableAppointmentReminders,
        enableHomeworkNotifications: event.enableHomeworkNotifications,
        enableExaminationResults: event.enableExaminationResults,
      ));
    } catch (e) {
      AppLogger.error(
        '❌ Failed to update notification settings',
        category: LogCategory.general,
        error: e,
      );

      emit(NotificationsError(
        message: 'فشل في تحديث إعدادات الإشعارات: ${e.toString()}',
      ));
    }
  }

  /// الاشتراك في موضوع
  Future<void> _onSubscribeToTopic(
    SubscribeToTopic event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      AppLogger.info(
        '📢 Subscribing to topic',
        category: LogCategory.general,
        data: {'topic': event.topic},
      );

      await _fcmService.subscribeToTopic(event.topic);

      emit(TopicSubscribed(topic: event.topic));
    } catch (e) {
      AppLogger.error(
        '❌ Failed to subscribe to topic',
        category: LogCategory.general,
        error: e,
      );

      emit(NotificationsError(
        message: 'فشل في الاشتراك في الموضوع: ${e.toString()}',
      ));
    }
  }

  /// إلغاء الاشتراك من موضوع
  Future<void> _onUnsubscribeFromTopic(
    UnsubscribeFromTopic event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      AppLogger.info(
        '📢 Unsubscribing from topic',
        category: LogCategory.general,
        data: {'topic': event.topic},
      );

      await _fcmService.unsubscribeFromTopic(event.topic);

      emit(TopicUnsubscribed(topic: event.topic));
    } catch (e) {
      AppLogger.error(
        '❌ Failed to unsubscribe from topic',
        category: LogCategory.general,
        error: e,
      );

      emit(NotificationsError(
        message: 'فشل في إلغاء الاشتراك من الموضوع: ${e.toString()}',
      ));
    }
  }
}