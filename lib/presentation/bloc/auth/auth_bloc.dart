import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;
import '../../../core/utils/app_logger.dart';
import '../../../core/services/shared_preferences_service.dart';
import '../../../core/services/fcm_integration_service.dart';
import '../../../data/repositories/patient_repository.dart';
import 'auth_events.dart';
import 'auth_states.dart';

/// Bloc للتعامل مع المصادقة
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final SupabaseClient _supabase;
  final PatientRepository _patientRepository;
  final SharedPreferencesService _prefs;
  final FCMIntegrationService _fcmIntegrationService;

  AuthBloc({
    required SupabaseClient supabase,
    required PatientRepository patientRepository,
    required SharedPreferencesService prefs,
    FCMIntegrationService? fcmIntegrationService,
  }) : _supabase = supabase,
       _patientRepository = patientRepository,
       _prefs = prefs,
       _fcmIntegrationService =
           fcmIntegrationService ?? FCMIntegrationService(),
       super(const AuthInitial()) {
    on<LoginRequested>(_onLoginRequested);
    on<SignUpRequested>(_onSignUpRequested);
    on<CheckAuthStatus>(_onCheckAuthStatus);
    on<LogoutRequested>(_onLogoutRequested);
    on<ResetPasswordRequested>(_onResetPasswordRequested);
  }

  /// معالجة طلب تسجيل الدخول
  Future<void> _onLoginRequested(
    LoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      AppLogger.info(
        'Login attempt started',
        category: LogCategory.auth,
        data: {'email': event.email},
      );

      // تسجيل الدخول في Supabase
      final response = await _supabase.auth.signInWithPassword(
        email: event.email.trim(),
        password: event.password,
      );

      if (response.user == null) {
        AppLogger.error(
          'Login failed: No user returned',
          category: LogCategory.auth,
        );
        emit(
          const AuthFailure(
            message: 'فشل في تسجيل الدخول. يرجى المحاولة مرة أخرى.',
            errorCode: 'no_user',
          ),
        );
        return;
      }

      final user = response.user!;
      AppLogger.info(
        'Supabase login successful',
        category: LogCategory.auth,
        data: {'userId': user.id},
      );

      // البحث عن بيانات المريض
      final patient = await _patientRepository.getPatientByUserId(user.id);

      if (patient == null) {
        AppLogger.error(
          'Patient not found for user',
          category: LogCategory.database,
          data: {'userId': user.id},
        );

        // تسجيل الخروج من Supabase
        await _supabase.auth.signOut();

        emit(
          const AuthFailure(
            message: 'لم يتم العثور على بيانات المريض. يرجى التواصل مع الدعم.',
            errorCode: 'patient_not_found',
          ),
        );
        return;
      }

      // حفظ بيانات تسجيل الدخول
      await _prefs.setLoggedIn(true);

      // حفظ FCM Token
      await _fcmIntegrationService.onUserLogin(user.id);

      AppLogger.info(
        'Login completed successfully',
        category: LogCategory.auth,
        data: {
          'userId': user.id,
          'patientId': patient.id,
          'patientName': patient.name,
        },
      );

      emit(AuthSuccess(patient: patient));
    } on AuthException catch (e) {
      AppLogger.error(
        'Supabase auth error during login',
        category: LogCategory.auth,
        error: e,
        data: {
          'email': event.email,
          'errorCode': e.statusCode,
          'errorMessage': e.message,
        },
      );

      String userMessage;
      switch (e.message.toLowerCase()) {
        case 'invalid login credentials':
          userMessage = 'البريد الإلكتروني أو كلمة السر غير صحيحة.';
          break;
        case 'email not confirmed':
          userMessage = 'يرجى تأكيد البريد الإلكتروني أولاً.';
          break;
        case 'too many requests':
          userMessage = 'تم تجاوز عدد المحاولات المسموح. يرجى المحاولة لاحقاً.';
          break;
        default:
          userMessage = 'حدث خطأ في تسجيل الدخول: ${e.message}';
      }

      emit(AuthFailure(message: userMessage, errorCode: e.statusCode));
    } catch (e) {
      AppLogger.error(
        'Unexpected error during login',
        category: LogCategory.auth,
        error: e,
        data: {'email': event.email},
      );

      emit(
        const AuthFailure(
          message: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
          errorCode: 'unexpected_error',
        ),
      );
    }
  }

  /// معالجة طلب إنشاء حساب جديد
  Future<void> _onSignUpRequested(
    SignUpRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      AppLogger.info(
        'Sign up attempt started',
        category: LogCategory.auth,
        data: {'email': event.email, 'name': event.name},
      );

      // إنشاء حساب في Supabase
      final response = await _supabase.auth.signUp(
        email: event.email.trim(),
        password: event.password,
        data: {'name': event.name, 'phone': event.phone},
      );

      if (response.user == null) {
        AppLogger.error(
          'Sign up failed: No user returned',
          category: LogCategory.auth,
        );
        emit(
          const AuthFailure(
            message: 'فشل في إنشاء الحساب. يرجى المحاولة مرة أخرى.',
            errorCode: 'no_user',
          ),
        );
        return;
      }

      final user = response.user!;
      AppLogger.info(
        'Supabase sign up successful',
        category: LogCategory.auth,
        data: {'userId': user.id},
      );

      // إنشاء سجل المريض
      final patient = await _patientRepository.createPatient(
        userId: user.id,
        name: event.name,
        email: event.email,
        phone: event.phone,
      );

      // حفظ بيانات تسجيل الدخول
      await _prefs.setLoggedIn(true);

      // حفظ FCM Token
      await _fcmIntegrationService.onUserLogin(user.id);

      AppLogger.info(
        'Sign up completed successfully',
        category: LogCategory.auth,
        data: {
          'userId': user.id,
          'patientId': patient.id,
          'patientName': patient.name,
        },
      );

      emit(AuthSuccess(patient: patient));
    } on AuthException catch (e) {
      AppLogger.error(
        'Supabase auth error during sign up',
        category: LogCategory.auth,
        error: e,
        data: {
          'email': event.email,
          'name': event.name,
          'errorCode': e.statusCode,
          'errorMessage': e.message,
        },
      );

      String userMessage;
      switch (e.message.toLowerCase()) {
        case 'user already registered':
          userMessage = 'هذا البريد الإلكتروني مسجل مسبقاً.';
          break;
        case 'password should be at least 6 characters':
          userMessage = 'كلمة السر يجب أن تكون 6 أحرف على الأقل.';
          break;
        case 'invalid email':
          userMessage = 'البريد الإلكتروني غير صحيح.';
          break;
        default:
          userMessage = 'حدث خطأ في إنشاء الحساب: ${e.message}';
      }

      emit(AuthFailure(message: userMessage, errorCode: e.statusCode));
    } catch (e) {
      AppLogger.error(
        'Unexpected error during sign up',
        category: LogCategory.auth,
        error: e,
        data: {'email': event.email, 'name': event.name},
      );

      emit(
        const AuthFailure(
          message: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
          errorCode: 'unexpected_error',
        ),
      );
    }
  }

  /// معالجة فحص حالة المصادقة
  Future<void> _onCheckAuthStatus(
    CheckAuthStatus event,
    Emitter<AuthState> emit,
  ) async {
    try {
      AppLogger.info('Checking auth status', category: LogCategory.auth);

      // فحص حالة تسجيل الدخول من SharedPreferences
      final isLoggedIn = await _prefs.isLoggedIn();
      if (!isLoggedIn) {
        AppLogger.info(
          'User not logged in (SharedPreferences)',
          category: LogCategory.auth,
        );
        emit(const AuthLoggedOut());
        return;
      }

      // فحص حالة المصادقة من Supabase
      final session = _supabase.auth.currentSession;
      final user = _supabase.auth.currentUser;

      if (session == null || user == null) {
        AppLogger.info(
          'No active Supabase session',
          category: LogCategory.auth,
        );

        // مسح بيانات SharedPreferences
        await _prefs.logout();
        emit(const AuthLoggedOut());
        return;
      }

      AppLogger.info(
        'Active session found, fetching patient data',
        category: LogCategory.auth,
        data: {'userId': user.id},
      );

      // البحث عن بيانات المريض
      final patient = await _patientRepository.getPatientByUserId(user.id);

      if (patient == null) {
        AppLogger.error(
          'Patient not found for authenticated user',
          category: LogCategory.database,
          data: {'userId': user.id},
        );

        // تسجيل الخروج
        await _supabase.auth.signOut();
        await _prefs.logout();

        emit(const AuthLoggedOut());
        return;
      }

      // التأكد من حفظ FCM Token
      await _fcmIntegrationService.onUserLogin(user.id);

      AppLogger.info(
        'Auth status check completed successfully',
        category: LogCategory.auth,
        data: {
          'userId': user.id,
          'patientId': patient.id,
          'patientName': patient.name,
        },
      );

      emit(AuthSuccess(patient: patient));
    } catch (e) {
      AppLogger.error(
        'Error checking auth status',
        category: LogCategory.auth,
        error: e,
      );

      // في حالة الخطأ، تسجيل الخروج
      await _prefs.logout();
      emit(const AuthLoggedOut());
    }
  }

  /// معالجة طلب تسجيل الخروج
  Future<void> _onLogoutRequested(
    LogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      AppLogger.info('Logout requested', category: LogCategory.auth);

      // الحصول على معرف المستخدم قبل تسجيل الخروج
      final currentUser = _supabase.auth.currentUser;
      final userId = currentUser?.id;

      // حذف FCM Token
      if (userId != null) {
        await _fcmIntegrationService.onUserLogout();
      }

      // تسجيل الخروج من Supabase
      await _supabase.auth.signOut();

      // حذف FCM Token

      // مسح بيانات SharedPreferences
      await _prefs.logout();

      AppLogger.info(
        'Logout completed successfully',
        category: LogCategory.auth,
      );

      emit(const AuthLoggedOut());
    } catch (e) {
      AppLogger.error(
        'Error during logout',
        category: LogCategory.auth,
        error: e,
      );

      // حتى لو فشل تسجيل الخروج، امسح البيانات المحلية
      await _prefs.logout();
      emit(const AuthLoggedOut());
    }
  }

  /// معالجة طلب إعادة تعيين كلمة السر
  Future<void> _onResetPasswordRequested(
    ResetPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      AppLogger.info(
        'Password reset requested',
        category: LogCategory.auth,
        data: {'email': event.email},
      );

      await _supabase.auth.resetPasswordForEmail(event.email.trim());

      AppLogger.info(
        'Password reset email sent',
        category: LogCategory.auth,
        data: {'email': event.email},
      );

      emit(ResetPasswordSent(email: event.email));
    } on AuthException catch (e) {
      AppLogger.error(
        'Error sending password reset email',
        category: LogCategory.auth,
        error: e,
        data: {'email': event.email},
      );

      emit(
        AuthFailure(
          message: 'فشل في إرسال رابط إعادة تعيين كلمة السر: ${e.message}',
          errorCode: e.statusCode,
        ),
      );
    } catch (e) {
      AppLogger.error(
        'Unexpected error during password reset',
        category: LogCategory.auth,
        error: e,
        data: {'email': event.email},
      );

      emit(
        const AuthFailure(
          message: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
          errorCode: 'unexpected_error',
        ),
      );
    }
  }
}
