import 'package:equatable/equatable.dart';

/// أحداث المصادقة
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// طلب تسجيل الدخول
class LoginRequested extends AuthEvent {
  final String email;
  final String password;

  const LoginRequested({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];

  @override
  String toString() => 'LoginRequested(email: $email)';
}

/// طلب إنشاء حساب جديد
class SignUpRequested extends AuthEvent {
  final String name;
  final String email;
  final String password;
  final String phone;
  final DateTime birthDate;
  final String gender;
  final List<String> treatmentTypes;

  const SignUpRequested({
    required this.name,
    required this.email,
    required this.password,
    required this.phone,
    required this.birthDate,
    required this.gender,
    required this.treatmentTypes,
  });

  @override
  List<Object?> get props => [name, email, password, phone, birthDate, gender, treatmentTypes];

  @override
  String toString() => 'SignUpRequested(name: $name, email: $email, gender: $gender)';
}

/// فحص حالة المصادقة
class CheckAuthStatus extends AuthEvent {
  const CheckAuthStatus();

  @override
  String toString() => 'CheckAuthStatus()';
}

/// طلب تسجيل الخروج
class LogoutRequested extends AuthEvent {
  const LogoutRequested();

  @override
  String toString() => 'LogoutRequested()';
}

/// طلب إعادة تعيين كلمة السر
class ResetPasswordRequested extends AuthEvent {
  final String email;

  const ResetPasswordRequested({required this.email});

  @override
  List<Object?> get props => [email];

  @override
  String toString() => 'ResetPasswordRequested(email: $email)';
}
