import 'package:equatable/equatable.dart';
import '../../../data/models/patient_model.dart';

/// حالات المصادقة
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class AuthInitial extends AuthState {
  const AuthInitial();

  @override
  String toString() => 'AuthInitial()';
}

/// حالة التحميل
class AuthLoading extends AuthState {
  const AuthLoading();

  @override
  String toString() => 'AuthLoading()';
}

/// حالة نجاح المصادقة
class AuthSuccess extends AuthState {
  final PatientModel patient;

  const AuthSuccess({required this.patient});

  @override
  List<Object?> get props => [patient];

  @override
  String toString() => 'AuthSuccess(patient: ${patient.name})';
}

/// حالة فشل المصادقة
class AuthFailure extends AuthState {
  final String message;
  final String? errorCode;

  const AuthFailure({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];

  @override
  String toString() => 'AuthFailure(message: $message, errorCode: $errorCode)';
}

/// حالة تسجيل الخروج
class AuthLoggedOut extends AuthState {
  const AuthLoggedOut();

  @override
  String toString() => 'AuthLoggedOut()';
}

/// حالة إرسال رابط إعادة تعيين كلمة السر
class ResetPasswordSent extends AuthState {
  final String email;

  const ResetPasswordSent({required this.email});

  @override
  List<Object?> get props => [email];

  @override
  String toString() => 'ResetPasswordSent(email: $email)';
}