import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/utils/app_logger.dart';
import '../../../../data/models/homework_model.dart';

/// تاب الواجبات - محدث للموديلز الجديدة
class HomeworkTab extends StatefulWidget {
  final String patientId;

  const HomeworkTab({super.key, required this.patientId});

  @override
  State<HomeworkTab> createState() => _HomeworkTabState();
}

class _HomeworkTabState extends State<HomeworkTab> {
  final SupabaseClient _supabase = Supabase.instance.client;
  bool _isLoading = true;
  List<HomeworkModel> _homework = [];

  @override
  void initState() {
    super.initState();
    _loadHomework();
  }

  Future<void> _loadHomework() async {
    AppLogger.info(
      '📚 Loading homework for patient: ${widget.patientId}',
      category: LogCategory.ui,
    );

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _supabase
          .from('homework')
          .select()
          .eq('patient_id', widget.patientId)
          .order('assigned_date', ascending: false);

      final homework =
          response.map((json) => HomeworkModel.fromJson(json)).toList();

      AppLogger.info(
        '✅ Loaded ${homework.length} homework items',
        category: LogCategory.ui,
        data: {'count': homework.length.toString()},
      );

      setState(() {
        _homework = homework;
        _isLoading = false;
      });
    } catch (e) {
      AppLogger.error(
        '❌ Error loading homework',
        category: LogCategory.ui,
        error: e,
      );

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        _showErrorSnackBar('فشل في تحميل الواجبات');
      }
    }
  }

  Future<void> _updateHomeworkStatus(
    HomeworkModel homework,
    String newStatus,
  ) async {
    try {
      AppLogger.info(
        '📝 Updating homework status',
        category: LogCategory.ui,
        data: {'homeworkId': homework.id, 'newStatus': newStatus},
      );

      await _supabase
          .from('homework')
          .update({
            'status': newStatus,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', homework.id);

      // تحديث قائمة الواجبات
      await _loadHomework();

      if (mounted) {
        _showSuccessSnackBar('تم تحديث حالة الواجب بنجاح');
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error updating homework status',
        category: LogCategory.ui,
        error: e,
      );

      if (mounted) {
        _showErrorSnackBar('فشل في تحديث حالة الواجب');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingState();
    }

    return Column(
      children: [_buildStatsRow(), Expanded(child: _buildHomeworkList())],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 40.w,
            height: 40.h,
            child: CircularProgressIndicator(
              strokeWidth: 3,
              color: AppColors.primary,
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            'جاري تحميل الواجبات...',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsRow() {
    final totalCount = _homework.length;
    final completedCount = _homework.where((hw) => hw.isCompleted).length;
    final overdueCount = _homework.where((hw) => hw.isOverdue).length;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: AppColors.primary, size: 16.sp),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              'إجمالي $totalCount واجب • مكتمل: $completedCount • متأخر: $overdueCount',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHomeworkList() {
    if (_homework.isEmpty) {
      return _buildEmptyState();
    }

    // ترتيب الواجبات حسب الأولوية
    final sortedHomework = List<HomeworkModel>.from(_homework);
    sortedHomework.sort((a, b) => b.priority.compareTo(a.priority));

    return RefreshIndicator(
      onRefresh: _loadHomework,
      color: AppColors.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: sortedHomework.length,
        itemBuilder: (context, index) {
          final homework = sortedHomework[index];
          return _buildHomeworkCard(homework);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    const emptyMessage = 'لا توجد واجبات';
    const emptySubtitle = 'لم يتم تكليفك بأي واجبات بعد';
    const emptyIcon = Icons.assignment;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(emptyIcon, size: 64.sp, color: AppColors.textSecondary),
          SizedBox(height: 16.h),
          Text(
            emptyMessage,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            emptySubtitle,
            style: TextStyle(fontSize: 14.sp, color: AppColors.textLight),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHomeworkCard(HomeworkModel homework) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: InkWell(
        onTap: () => _showHomeworkDetails(homework),
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: _getStatusColor(homework.status).withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس البطاقة
                _buildCardHeader(homework),
                SizedBox(height: 16.h),

                // معلومات الواجب
                _buildHomeworkInfo(homework),

                // الوصف (مختصر)
                if (homework.hasDescription) ...[
                  SizedBox(height: 12.h),
                  _buildDescriptionPreview(homework),
                ],

                // ملف PDF
                if (homework.hasPdf) ...[
                  SizedBox(height: 12.h),
                  _buildPdfSection(homework),
                ],

                // مؤشر للنقر
                SizedBox(height: 12.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.touch_app,
                      size: 16.sp,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      'اضغط لعرض التفاصيل',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCardHeader(HomeworkModel homework) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8.w),
          decoration: BoxDecoration(
            color: _getStatusColor(homework.status).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Icon(
            _getStatusIcon(homework.status),
            color: _getStatusColor(homework.status),
            size: 20.sp,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                homework.displayTitle,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Text(
                homework.formattedAssignedDate,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
        _buildStatusChip(homework),
      ],
    );
  }

  Widget _buildHomeworkInfo(HomeworkModel homework) {
    return Column(
      children: [
        _buildInfoRow(
          Icons.calendar_today,
          'تاريخ التكليف',
          homework.formattedAssignedDate,
        ),
        SizedBox(height: 8.h),
        _buildInfoRow(Icons.schedule, 'منذ', homework.timeFromNow),
        if (homework.createdAt != null) ...[
          SizedBox(height: 8.h),
          _buildInfoRow(
            Icons.add_circle_outline,
            'تاريخ الإضافة',
            homework.formattedCreatedDate,
          ),
        ],
      ],
    );
  }

  Widget _buildDescriptionPreview(HomeworkModel homework) {
    final description = homework.description ?? '';
    final preview =
        description.length > 100
            ? '${description.substring(0, 100)}...'
            : description;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.description, size: 16.sp, color: AppColors.primary),
              SizedBox(width: 8.w),
              Text(
                'الوصف',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            preview,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection(HomeworkModel homework) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description,
                color: AppColors.textSecondary,
                size: 16.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'وصف الواجب',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            homework.description!,
            style: TextStyle(fontSize: 14.sp, color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionsSection(HomeworkModel homework) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.list_alt, color: AppColors.primary, size: 16.sp),
              SizedBox(width: 8.w),
              Text(
                'تعليمات التنفيذ',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            homework.instructions!,
            style: TextStyle(fontSize: 14.sp, color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(HomeworkModel homework) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.warning.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.note, color: AppColors.warning, size: 16.sp),
              SizedBox(width: 8.w),
              Text(
                'ملاحظات',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.warning,
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Text(
            homework.notes!,
            style: TextStyle(fontSize: 14.sp, color: AppColors.textPrimary),
          ),
        ],
      ),
    );
  }

  Widget _buildPdfSection(HomeworkModel homework) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.success.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.success.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(Icons.picture_as_pdf, color: AppColors.success, size: 16.sp),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              'ملف PDF متاح',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.success,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          TextButton(
            onPressed: () => _openPdf(homework),
            child: Text(
              'فتح',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.success,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(HomeworkModel homework) {
    return Row(
      children: [
        if (homework.isAssigned) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _updateHomeworkStatus(homework, 'in_progress'),
              icon: Icon(Icons.play_arrow, size: 16.sp),
              label: const Text('بدء التنفيذ'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),
        ],
        if (homework.isInProgress) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _updateHomeworkStatus(homework, 'completed'),
              icon: Icon(Icons.check, size: 16.sp),
              label: const Text('تم الانتهاء'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.success,
                foregroundColor: AppColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _updateHomeworkStatus(homework, 'assigned'),
              icon: Icon(Icons.pause, size: 16.sp),
              label: const Text('إيقاف مؤقت'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.warning,
                side: BorderSide(color: AppColors.warning),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatusChip(HomeworkModel homework) {
    final statusColor = _getStatusColor(homework.status);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        homework.statusText,
        style: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
          color: statusColor,
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, color: AppColors.textSecondary, size: 16.sp),
        SizedBox(width: 8.w),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'assigned':
        return AppColors.primary;
      case 'in_progress':
        return AppColors.warning;
      case 'completed':
        return AppColors.success;
      case 'overdue':
        return AppColors.error;
      case 'cancelled':
        return AppColors.textSecondary;
      default:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'assigned':
        return Icons.assignment_outlined;
      case 'in_progress':
        return Icons.assignment_ind;
      case 'completed':
        return Icons.assignment_turned_in;
      case 'overdue':
        return Icons.assignment_late;
      case 'cancelled':
        return Icons.assignment_return;
      default:
        return Icons.assignment;
    }
  }

  Future<void> _openPdf(HomeworkModel homework) async {
    try {
      final uri = Uri.parse(homework.pdfUrl!);

      AppLogger.info(
        '📄 Opening PDF',
        category: LogCategory.ui,
        data: {'homeworkId': homework.id},
      );

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'لا يمكن فتح ملف PDF';
      }
    } catch (e) {
      AppLogger.error(
        '❌ Error opening PDF',
        category: LogCategory.ui,
        error: e,
      );

      if (mounted) {
        _showErrorSnackBar('خطأ في فتح ملف PDF');
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showHomeworkDetails(HomeworkModel homework) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // شريط العنوان
                  Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: _getStatusColor(
                        homework.status,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16.r),
                        topRight: Radius.circular(16.r),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(8.w),
                          decoration: BoxDecoration(
                            color: _getStatusColor(
                              homework.status,
                            ).withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Icon(
                            _getStatusIcon(homework.status),
                            color: _getStatusColor(homework.status),
                            size: 20.sp,
                          ),
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                homework.displayTitle,
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                              Text(
                                homework.formattedAssignedDate,
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ),
                        _buildStatusChip(homework),
                        SizedBox(width: 8.w),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: Icon(
                            Icons.close,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // المحتوى
                  Flexible(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // معلومات الواجب
                          _buildHomeworkInfo(homework),

                          // الوصف
                          if (homework.hasDescription) ...[
                            SizedBox(height: 16.h),
                            _buildDescriptionSection(homework),
                          ],

                          // التعليمات
                          if (homework.hasInstructions) ...[
                            SizedBox(height: 16.h),
                            _buildInstructionsSection(homework),
                          ],

                          // الملاحظات
                          if (homework.hasNotes) ...[
                            SizedBox(height: 16.h),
                            _buildNotesSection(homework),
                          ],

                          // ملف PDF
                          if (homework.hasPdf) ...[
                            SizedBox(height: 16.h),
                            _buildPdfSection(homework),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }
}
