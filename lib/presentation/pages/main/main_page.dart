import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/utils/app_logger.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_states.dart';
import '../appointments/appointments_page.dart';
import '../products/products_page.dart';
import '../profile/profile_page.dart';
import '../medical_record/medical_record_page.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide AuthState;

/// الصفحة الرئيسية مع التنقل السفلي - IIHC
class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  int _currentIndex = 0;
  final Map<int, Widget> _cachedPages = {}; // Cache للصفحات المحملة

  void _onTabTapped(int index) {
    final pageNames = [
      'Appointments',
      'Medical Record',
      'Products',
      'Profile',
    ];
    final currentPageName =
        pageNames.length > _currentIndex ? pageNames[_currentIndex] : 'Unknown';
    final targetPageName =
        pageNames.length > index ? pageNames[index] : 'Unknown';

    AppLogger.navigation(currentPageName, targetPageName);

    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        // تحديد القيم الافتراضية
        String authId = '';

        // إذا كانت البيانات متاحة، استخدمها
        if (state is AuthSuccess) {
          // استخدام معرف المستخدم من Supabase مباشرة
          final currentUser = Supabase.instance.client.auth.currentUser;
          authId = currentUser?.id ?? '';
        } else {
          // استخدام بيانات افتراضية أو من Supabase مباشرة
          final currentUser = Supabase.instance.client.auth.currentUser;
          authId = currentUser?.id ?? '';
        }

        return Scaffold(
          body: _buildCurrentPage(authId),
          bottomNavigationBar: _buildBottomNavigationBar(),
        );
      },
    );
  }

  /// بناء الصفحة الحالية فقط (Lazy Loading)
  Widget _buildCurrentPage(String authId) {
    // إذا كانت الصفحة محفوظة في Cache، استخدمها
    if (_cachedPages.containsKey(_currentIndex)) {
      return _cachedPages[_currentIndex]!;
    }

    // بناء الصفحة الجديدة وحفظها في Cache
    Widget page;

    // ترتيب الصفحات الجديد - نفس الترتيب لجميع المستخدمين
    switch (_currentIndex) {
      case 0:
        page = AppointmentsPage(authId: authId); // المواعيد
        break;
      case 1:
        page = _buildMedicalRecordPage(); // السجل الطبي
        break;
      case 2:
        page = const ProductsPage(); // المنتجات
        break;
      case 3:
        page = const ProfilePage(); // الملف الشخصي
        break;
      default:
        page = AppointmentsPage(authId: authId);
    }

    // حفظ الصفحة في Cache
    _cachedPages[_currentIndex] = page;
    return page;
  }

  Widget _buildMedicalRecordPage() {
    // الحصول على معرف المستخدم الحالي
    final currentUser = Supabase.instance.client.auth.currentUser;

    if (currentUser != null) {
      return MedicalRecordPage(authId: currentUser.id);
    } else {
      // في حالة عدم وجود مستخدم، عرض صفحة فارغة
      return const MedicalRecordPage();
    }
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: _onTabTapped,
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppColors.white,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textLight,
        selectedLabelStyle: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontFamily: 'Cairo',
          fontSize: 12.sp,
          fontWeight: FontWeight.w400,
        ),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.event_note_rounded),
            label: AppStrings.appointments,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.health_and_safety_rounded),
            label: AppStrings.medicalRecord,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.storefront_rounded),
            label: AppStrings.products,
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_circle_rounded),
            label: AppStrings.profile,
          ),
        ],
      ),
    );
  }
}