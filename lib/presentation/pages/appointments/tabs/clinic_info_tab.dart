import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/utils/app_logger.dart';
import '../../../../data/models/clinic_info_model.dart';

/// تاب معلومات العيادة - محدث بالكامل ليتناسب مع جدول معلومات العيادة
class ClinicInfoTab extends StatefulWidget {
  const ClinicInfoTab({super.key});

  @override
  State<ClinicInfoTab> createState() => _ClinicInfoTabState();
}

class _ClinicInfoTabState extends State<ClinicInfoTab> {
  final SupabaseClient _supabase = Supabase.instance.client;
  List<ClinicInfoModel> _clinicInfo = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadClinicInfo();
  }

  /// تحميل معلومات العيادة من قاعدة البيانات
  Future<void> _loadClinicInfo() async {
    AppLogger.info('🏥 بدء تحميل معلومات العيادة', category: LogCategory.ui);

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final response = await _supabase
          .from('clinic_info')
          .select('*')
          .eq('is_active', true)
          .order('display_order', ascending: true);

      AppLogger.info(
        '📊 تم استلام البيانات من قاعدة البيانات',
        category: LogCategory.ui,
        data: {'count': response.length.toString()},
      );

      final clinicInfoList = response
          .map((json) => ClinicInfoModel.fromJson(json))
          .toList();

      setState(() {
        _clinicInfo = clinicInfoList;
        _isLoading = false;
      });

      AppLogger.info(
        '✅ تم تحميل معلومات العيادة بنجاح',
        category: LogCategory.ui,
        data: {'totalItems': clinicInfoList.length.toString()},
      );

    } catch (e) {
      AppLogger.error(
        '❌ خطأ في تحميل معلومات العيادة',
        category: LogCategory.ui,
        error: e,
      );

      setState(() {
        _isLoading = false;
        _errorMessage = 'حدث خطأ في تحميل معلومات العيادة';
        _clinicInfo = [];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: RefreshIndicator(
        onRefresh: _loadClinicInfo,
        color: AppColors.primary,
        child: _isLoading ? _buildLoadingWidget() : _buildContent(),
      ),
    );
  }

  /// واجهة التحميل
  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
            strokeWidth: 3.w,
          ),
          SizedBox(height: 20.h),
          Text(
            'جاري تحميل معلومات العيادة...',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// المحتوى الرئيسي
  Widget _buildContent() {
    if (_clinicInfo.isEmpty && _errorMessage == null) {
      return _buildEmptyState();
    }

    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeaderCard(),
          SizedBox(height: 24.h),
          if (_errorMessage != null) _buildErrorMessage(),
          if (_clinicInfo.isNotEmpty) _buildInfoGrid(),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  /// حالة عدم وجود بيانات
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: 80.sp,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          SizedBox(height: 20.h),
          Text(
            'لا توجد معلومات عيادة متاحة',
            style: TextStyle(
              fontSize: 18.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'اسحب للأسفل لإعادة التحميل',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textSecondary.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// بطا��ة الرأس
  Widget _buildHeaderCard() {
    final clinicName = _getInfoByKey('clinic_name')?.displayInfoValue ?? 
                      AppStrings.appName;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          children: [
            // شعار العيادة
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: AppColors.white, width: 4.w),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.black.withValues(alpha: 0.2),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: CircleAvatar(
                radius: 50.r,
                backgroundImage: const AssetImage('assets/images/logo.jpeg'),
                backgroundColor: AppColors.white,
              ),
            ),
            SizedBox(height: 20.h),

            // اسم العيادة
            Text(
              clinicName,
              style: TextStyle(
                fontSize: 22.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.white,
                height: 1.3,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8.h),

            // شعار أو وصف
            Text(
              'نحو حياة أفضل بسمع ونطق سليم',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.white.withValues(alpha: 0.9),
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// رسالة الخطأ
  Widget _buildErrorMessage() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: AppColors.error,
            size: 24.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.error,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// شبكة المعلومات
  Widget _buildInfoGrid() {
    final groupedInfo = _groupInfoByType();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات العيادة',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 16.h),
        
        ...groupedInfo.entries.map((entry) {
          return Column(
            children: [
              _buildInfoSection(entry.key, entry.value),
              SizedBox(height: 16.h),
            ],
          );
        }),
      ],
    );
  }

  /// تجميع المعلومات حسب النوع
  Map<String, List<ClinicInfoModel>> _groupInfoByType() {
    final grouped = <String, List<ClinicInfoModel>>{};
    
    for (final info in _clinicInfo) {
      if (!grouped.containsKey(info.infoType)) {
        grouped[info.infoType] = [];
      }
      grouped[info.infoType]!.add(info);
    }
    
    // ترتيب كل مجموعة حسب display_order
    for (final list in grouped.values) {
      list.sort((a, b) => a.displayOrder.compareTo(b.displayOrder));
    }
    
    return grouped;
  }

  /// قسم المعلومات
  Widget _buildInfoSection(String sectionType, List<ClinicInfoModel> items) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(10.w),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10.r),
                  ),
                  child: Icon(
                    _getSectionIcon(sectionType),
                    color: AppColors.primary,
                    size: 24.sp,
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Text(
                    _getSectionTitle(sectionType),
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),

            // عناصر القسم
            ...items.map((item) => _buildInfoItem(item)),
          ],
        ),
      ),
    );
  }

  /// عنصر معلومة واحد
  Widget _buildInfoItem(ClinicInfoModel info) {
    final isClickable = _isInfoClickable(info);

    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      child: InkWell(
        onTap: isClickable ? () => _handleInfoTap(info) : null,
        onLongPress: _isPhoneNumber(info) ? () => _showPhoneOptions(info) : null,
        borderRadius: BorderRadius.circular(12.r),
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: isClickable 
                  ? AppColors.primary.withValues(alpha: 0.3)
                  : AppColors.border,
              width: 1.w,
            ),
          ),
          child: Row(
            children: [
              // أيقونة
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: isClickable
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : AppColors.textSecondary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  _getInfoIcon(info.iconName ?? ''),
                  color: isClickable ? AppColors.primary : AppColors.textSecondary,
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 16.w),

              // المحتوى
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      info.displayNameText,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      info.formattedValue,
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: AppColors.textSecondary,
                        height: 1.4,
                      ),
                    ),
                    if (info.hasDescription) ...[
                      SizedBox(height: 4.h),
                      Text(
                        info.description!,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary.withValues(alpha: 0.7),
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // أيقونة الإجراء
              if (isClickable) ...[
                SizedBox(width: 8.w),
                Icon(
                  _getActionIcon(info),
                  size: 16.sp,
                  color: AppColors.primary,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Helper Methods

  ClinicInfoModel? _getInfoByKey(String key) {
    try {
      return _clinicInfo.firstWhere((info) => info.key == key);
    } catch (e) {
      return null;
    }
  }

  bool _isInfoClickable(ClinicInfoModel info) {
    return _isPhoneNumber(info) || 
           _isEmail(info) || 
           _isSocialMedia(info) || 
           _isWebsite(info);
  }

  bool _isPhoneNumber(ClinicInfoModel info) {
    return info.isPhoneType || 
           info.iconName == 'phone' || 
           info.iconName == 'emergency' ||
           info.dataType.toLowerCase() == 'phone';
  }

  bool _isEmail(ClinicInfoModel info) {
    return info.isEmailType || 
           info.iconName == 'email' ||
           info.dataType.toLowerCase() == 'email';
  }

  bool _isSocialMedia(ClinicInfoModel info) {
    return info.infoType.toLowerCase() == 'social_media' ||
           info.infoType.toLowerCase() == 'social' ||
           ['facebook', 'instagram', 'twitter', 'whatsapp', 'telegram', 'youtube']
               .contains(info.iconName?.toLowerCase());
  }

  bool _isWebsite(ClinicInfoModel info) {
    return info.isUrlType || 
           info.iconName == 'website' ||
           info.dataType.toLowerCase() == 'url' ||
           info.value.startsWith('http');
  }

  IconData _getActionIcon(ClinicInfoModel info) {
    if (_isPhoneNumber(info)) return Icons.phone;
    if (_isEmail(info)) return Icons.email;
    if (_isSocialMedia(info) || _isWebsite(info)) return Icons.open_in_new;
    return Icons.arrow_forward_ios;
  }

  String _getSectionTitle(String sectionType) {
    switch (sectionType.toLowerCase()) {
      case 'general':
        return 'معلومات عامة';
      case 'contact':
        return 'معلومات التواصل';
      case 'address':
        return 'العنوان والموقع';
      case 'working_hours':
        return 'مواعيد العمل';
      case 'service':
        return 'الخدمات';
      case 'social_media':
      case 'social':
        return 'وسائل التواصل الاجتماعي';
      case 'setting':
        return 'الإعدادات';
      default:
        return 'معلومات إضافية';
    }
  }

  IconData _getSectionIcon(String sectionType) {
    switch (sectionType.toLowerCase()) {
      case 'general':
        return Icons.info_outline;
      case 'contact':
        return Icons.contact_phone;
      case 'address':
        return Icons.location_on;
      case 'working_hours':
        return Icons.access_time;
      case 'service':
        return Icons.medical_services;
      case 'social_media':
      case 'social':
        return Icons.share;
      case 'setting':
        return Icons.settings;
      default:
        return Icons.info;
    }
  }

  IconData _getInfoIcon(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'phone':
        return Icons.phone;
      case 'email':
        return Icons.email;
      case 'location_on':
        return Icons.location_on;
      case 'schedule':
        return Icons.schedule;
      case 'local_hospital':
        return Icons.local_hospital;
      case 'emergency':
        return Icons.emergency;
      case 'website':
        return Icons.language;
      case 'whatsapp':
        return Icons.chat;
      case 'facebook':
        return Icons.facebook;
      case 'instagram':
        return Icons.camera_alt;
      case 'twitter':
        return Icons.alternate_email;
      case 'youtube':
        return Icons.play_circle_outline;
      case 'telegram':
        return Icons.send;
      default:
        return Icons.info;
    }
  }

  // Action Methods

  Future<void> _handleInfoTap(ClinicInfoModel info) async {
    try {
      if (_isPhoneNumber(info)) {
        await _makePhoneCall(info.value);
      } else if (_isEmail(info)) {
        await _sendEmail(info.value);
      } else if (_isSocialMedia(info) || _isWebsite(info)) {
        await _openUrl(info.value);
      }
    } catch (e) {
      AppLogger.error('خطأ في التعامل مع النقر', error: e);
      _showErrorSnackBar('حدث خطأ في فتح ${info.displayNameText}');
    }
  }

  void _showPhoneOptions(ClinicInfoModel info) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            SizedBox(height: 20.h),
            
            Text(
              info.displayNameText,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 8.h),
            
            Text(
              info.formattedValue,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 24.h),
            
            Row(
              children: [
                Expanded(
                  child: _buildBottomSheetButton(
                    icon: Icons.phone,
                    label: 'اتصال',
                    color: AppColors.success,
                    onTap: () {
                      Navigator.pop(context);
                      _makePhoneCall(info.value);
                    },
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: _buildBottomSheetButton(
                    icon: Icons.copy,
                    label: 'نسخ',
                    color: AppColors.info,
                    onTap: () {
                      Navigator.pop(context);
                      _copyToClipboard(info.value, 'تم نسخ رقم الهاتف');
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSheetButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24.sp),
            SizedBox(height: 8.h),
            Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final uri = Uri.parse('tel:$phoneNumber');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      _showErrorSnackBar('لا يمكن إجراء المكالمة');
    }
  }

  Future<void> _sendEmail(String email) async {
    final uri = Uri.parse('mailto:$email');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      _showErrorSnackBar('لا يمكن فتح تطبيق البريد الإلكتروني');
    }
  }

  Future<void> _openUrl(String url) async {
    // تحسين معالجة الروابط
    String finalUrl = url;
    
    // إضافة https إذا لم يكن موجود
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      finalUrl = 'https://$url';
    }
    
    // معالجة خاصة لوسائل التواصل الاجتماعي
    if (url.contains('facebook.com') && !url.startsWith('http')) {
      finalUrl = 'https://www.facebook.com/$url';
    } else if (url.contains('instagram.com') && !url.startsWith('http')) {
      finalUrl = 'https://www.instagram.com/$url';
    } else if (url.contains('twitter.com') && !url.startsWith('http')) {
      finalUrl = 'https://www.twitter.com/$url';
    }
    
    final uri = Uri.parse(finalUrl);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      _showErrorSnackBar('لا يمكن فتح الرابط');
    }
  }

  void _copyToClipboard(String text, String message) {
    Clipboard.setData(ClipboardData(text: text));
    _showSuccessSnackBar(message);
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontSize: 14.sp, color: AppColors.white),
        ),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        margin: EdgeInsets.all(16.w),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(fontSize: 14.sp, color: AppColors.white),
        ),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        margin: EdgeInsets.all(16.w),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}