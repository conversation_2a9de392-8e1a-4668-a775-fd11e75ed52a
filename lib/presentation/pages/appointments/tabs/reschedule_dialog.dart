import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/utils/app_logger.dart';
import '../../../../data/models/appointment_model.dart';

/// Dialog لتغيير موعد الحجز
class RescheduleAppointmentDialog extends StatefulWidget {
  final AppointmentModel appointment;
  final VoidCallback onReschedule;

  const RescheduleAppointmentDialog({
    super.key,
    required this.appointment,
    required this.onReschedule,
  });

  @override
  State<RescheduleAppointmentDialog> createState() => _RescheduleAppointmentDialogState();
}

class _RescheduleAppointmentDialogState extends State<RescheduleAppointmentDialog> {
  final SupabaseClient _supabase = Supabase.instance.client;
  
  DateTime? _selectedDate;
  String? _selectedTimeSlotId;
  bool _isLoading = false;
  List<Map<String, dynamic>> _availableTimeSlots = [];
  Map<String, dynamic>? _holidayInfo;

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: AlertDialog(
        title: Text(
          'تغيير موعد الحجز',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // اختيار التاريخ
              ListTile(
                leading: Icon(Icons.calendar_today, color: AppColors.primary),
                title: Text(_selectedDate == null 
                    ? 'اختر التاريخ الجديد' 
                    : '${_getArabicDayName(_selectedDate!)} - ${_formatDate(_selectedDate!)}'),
                onTap: _selectDate,
              ),
              
              // عرض معلومات الأجازة إن وجدت
              if (_holidayInfo != null) ...[
                SizedBox(height: 8.h),
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: AppColors.warning.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(color: AppColors.warning.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.warning, color: AppColors.warning, size: 20.sp),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'يوم أجازة',
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.bold,
                                color: AppColors.warning,
                              ),
                            ),
                            Text(
                              _holidayInfo!['reason'] ?? 'أجازة',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // عرض الأوقات المتاحة
              if (_selectedDate != null && _holidayInfo == null) ...[
                SizedBox(height: 16.h),
                Text(
                  'الأوقات المتاحة',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8.h),
                if (_isLoading)
                  Center(child: CircularProgressIndicator(color: AppColors.primary))
                else if (_availableTimeSlots.isEmpty)
                  Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: AppColors.background,
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: AppColors.border),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.schedule_outlined,
                          size: 48.sp,
                          color: AppColors.textSecondary,
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          'لا توجد أوقات متاحة في هذا اليوم',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )
                else
                  SizedBox(
                    height: 250.h,
                    child: ListView.builder(
                      itemCount: _availableTimeSlots.length,
                      itemBuilder: (context, index) {
                        final timeSlot = _availableTimeSlots[index];
                        final isSelected = _selectedTimeSlotId == timeSlot['id'];
                        
                        return Card(
                          margin: EdgeInsets.only(bottom: 8.h),
                          elevation: isSelected ? 4 : 1,
                          child: ListTile(
                            selected: isSelected,
                            selectedTileColor: AppColors.primary.withValues(alpha: 0.1),
                            leading: Container(
                              padding: EdgeInsets.all(8.w),
                              decoration: BoxDecoration(
                                color: isSelected 
                                    ? AppColors.primary 
                                    : AppColors.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Icon(
                                Icons.access_time,
                                color: isSelected ? Colors.white : AppColors.primary,
                                size: 20.sp,
                              ),
                            ),
                            title: Text(
                              '${_formatTimeTo12Hour(timeSlot['start_time'])} - ${_formatTimeTo12Hour(timeSlot['end_time'])}',
                              style: TextStyle(
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                                color: isSelected ? AppColors.primary : AppColors.textPrimary,
                                fontSize: 14.sp,
                              ),
                            ),
                            subtitle: Text(
                              timeSlot['employee_name'] != null 
                                  ? 'د. ${timeSlot['employee_name']} (${timeSlot['specialization_name'] ?? 'أخصائي'})'
                                  : 'غير محدد',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: AppColors.textSecondary,
                              ),
                            ),
                            trailing: isSelected 
                                ? Icon(
                                    Icons.check_circle,
                                    color: AppColors.primary,
                                    size: 24.sp,
                                  )
                                : Icon(
                                    Icons.radio_button_unchecked,
                                    color: AppColors.textSecondary,
                                    size: 24.sp,
                                  ),
                            onTap: () {
                              setState(() {
                                _selectedTimeSlotId = timeSlot['id'];
                              });
                            },
                          ),
                        );
                      },
                    ),
                  ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'إلغاء',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: _selectedTimeSlotId != null ? _confirmReschedule : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: Text('تأكيد التغيير'),
          ),
        ],
      ),
    );
  }

  String _getArabicDayName(DateTime date) {
    const arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return arabicDays[date.weekday - 1];
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTimeTo12Hour(String time24) {
    if (time24.isEmpty) return 'غير محدد';
    
    try {
      final parts = time24.split(':');
      if (parts.length < 2) return time24;
      
      int hour = int.parse(parts[0]);
      int minute = int.parse(parts[1]);
      
      String period = hour >= 12 ? 'م' : 'ص';
      
      if (hour == 0) {
        hour = 12;
      } else if (hour > 12) {
        hour = hour - 12;
      }
      
      return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return time24;
    }
  }

  String? _getSelectedEmployeeId() {
    if (_selectedTimeSlotId == null) return null;
    
    final selectedTimeSlot = _availableTimeSlots.firstWhere(
      (slot) => slot['id'] == _selectedTimeSlotId,
      orElse: () => {},
    );
    
    return selectedTimeSlot['employee_id'];
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(Duration(days: 365)),
      locale: Locale('ar'),
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
        _selectedTimeSlotId = null;
        _availableTimeSlots.clear();
        _holidayInfo = null;
      });

      await _checkHolidayAndLoadTimeSlots();
    }
  }

  Future<void> _checkHolidayAndLoadTimeSlots() async {
    if (_selectedDate == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      AppLogger.info(
        '📅 Checking holiday and loading time slots for: ${_getArabicDayName(_selectedDate!)} - ${_formatDate(_selectedDate!)}',
        category: LogCategory.ui,
      );

      // فحص الأجازات - مع معالجة أفضل للأخطاء
      bool isHoliday = false;
      String holidayReason = 'أجازة';
      
      try {
        final holidayResponse = await _supabase
            .from('holidays')
            .select('*')
            .eq('date', _selectedDate!.toIso8601String().split('T')[0])
            .maybeSingle();

        if (holidayResponse != null) {
          AppLogger.info('🏖️ Holiday found for this date', category: LogCategory.ui);
          isHoliday = true;
          
          // تحديد سبب الأجازة من الأعمدة المتاحة
          if (holidayResponse.containsKey('reason')) {
            holidayReason = holidayResponse['reason'] ?? 'أجازة';
          } else if (holidayResponse.containsKey('description')) {
            holidayReason = holidayResponse['description'] ?? 'أجازة';
          } else if (holidayResponse.containsKey('name')) {
            holidayReason = holidayResponse['name'] ?? 'أجازة';
          } else if (holidayResponse.containsKey('title')) {
            holidayReason = holidayResponse['title'] ?? 'أجازة';
          } else if (holidayResponse.containsKey('holiday_name')) {
            holidayReason = holidayResponse['holiday_name'] ?? 'أجازة';
          } else {
            // إذا لم نجد أي عمود مناسب، نستخدم النص الافتراضي
            holidayReason = 'يوم أجازة رسمية';
          }
        }
      } catch (holidayError) {
        AppLogger.warning(
          '⚠️ Could not check holidays table, proceeding with time slots',
          category: LogCategory.ui,
          error: holidayError,
        );
        // في حالة خطأ في فحص الأجازات، نكمل تحميل الأوقات
      }

      if (isHoliday) {
        setState(() {
          _holidayInfo = {'reason': holidayReason};
          _isLoading = false;
        });
        return;
      }

      // تحميل الأوقات المتاحة
      await _loadAvailableTimeSlots();
    } catch (e) {
      AppLogger.error('❌ Error checking holiday and loading time slots', category: LogCategory.ui, error: e);
      // في حالة الخطأ، نحاول تحميل الأوقات على أي حال
      await _loadAvailableTimeSlots();
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAvailableTimeSlots() async {
    if (_selectedDate == null) return;

    try {
      // حساب day_of_week الصحيح (الأحد = 0, الاثنين = 1, إلخ)
      int dayOfWeek;
      switch (_selectedDate!.weekday) {
        case DateTime.sunday:
          dayOfWeek = 0;
          break;
        case DateTime.monday:
          dayOfWeek = 1;
          break;
        case DateTime.tuesday:
          dayOfWeek = 2;
          break;
        case DateTime.wednesday:
          dayOfWeek = 3;
          break;
        case DateTime.thursday:
          dayOfWeek = 4;
          break;
        case DateTime.friday:
          dayOfWeek = 5;
          break;
        case DateTime.saturday:
          dayOfWeek = 6;
          break;
        default:
          dayOfWeek = 0;
      }

      AppLogger.info(
        '📅 Loading time slots for day: ${_getArabicDayName(_selectedDate!)} (day_of_week: $dayOfWeek)',
        category: LogCategory.ui,
      );

      // تحميل جميع الأوقات لهذا اليوم
      final timeSlotsResponse = await _supabase
          .from('time_slots')
          .select('id, start_time, end_time, employee_id')
          .eq('day_of_week', dayOfWeek)
          .order('start_time');

      AppLogger.info(
        '⏰ Found ${timeSlotsResponse.length} time slots for this day',
        category: LogCategory.ui,
      );

      if (timeSlotsResponse.isEmpty) {
        setState(() {
          _availableTimeSlots = [];
        });
        return;
      }

      // تحميل الحجوزات الموجودة لهذا التاريخ
      final appointmentsResponse = await _supabase
          .from('appointments')
          .select('time_slot_id, status')
          .eq('appointment_date', _selectedDate!.toIso8601String().split('T')[0]);

      AppLogger.info(
        '📋 Found ${appointmentsResponse.length} appointments for this date',
        category: LogCategory.ui,
      );

      // فلترة الأوقات المحجوزة (استبعاد المؤكدة والمكتملة ولم يحضر)
      final unavailableTimeSlotIds = <String>{};
      for (final appointment in appointmentsResponse) {
        final timeSlotId = appointment['time_slot_id'];
        final status = appointment['status']?.toString().toLowerCase();
        
        if (timeSlotId != null && status != null) {
          // استبعاد الحالات: confirmed, completed, no_show, booked
          // السماح بـ: cancelled فقط
          if (status == 'confirmed' || 
              status == 'completed' || 
              status == 'no_show' || 
              status == 'booked') {
            unavailableTimeSlotIds.add(timeSlotId);
          }
        }
      }

      AppLogger.info(
        '🚫 ${unavailableTimeSlotIds.length} time slots are unavailable',
        category: LogCategory.ui,
      );

      // فلترة الأوقات المتاحة
      final availableSlots = timeSlotsResponse
          .where((slot) => !unavailableTimeSlotIds.contains(slot['id']))
          .toList();

      AppLogger.info(
        '✅ ${availableSlots.length} time slots are available',
        category: LogCategory.ui,
      );

      // تحميل معلومات الأخصائيين
      final employeeIds = availableSlots
          .map((slot) => slot['employee_id'])
          .where((id) => id != null)
          .toSet()
          .toList();

      Map<String, Map<String, dynamic>> employees = {};
      for (final employeeId in employeeIds) {
        try {
          final employeeResponse = await _supabase
              .from('admins')
              .select('id, name, specialization_name')
              .eq('id', employeeId)
              .single();
          employees[employeeId] = employeeResponse;
        } catch (e) {
          AppLogger.warning('⚠️ Could not load employee: $employeeId', category: LogCategory.ui);
        }
      }

      // إضافة معلومات الأخصائي لكل slot
      for (final slot in availableSlots) {
        final employeeId = slot['employee_id'];
        if (employeeId != null && employees.containsKey(employeeId)) {
          slot['employee_name'] = employees[employeeId]!['name'];
          slot['specialization_name'] = employees[employeeId]!['specialization_name'];
        } else {
          slot['employee_name'] = 'غير محدد';
          slot['specialization_name'] = 'عام';
        }
      }

      setState(() {
        _availableTimeSlots = availableSlots;
      });

      AppLogger.info(
        '🎯 Successfully loaded ${availableSlots.length} available time slots with employee info',
        category: LogCategory.ui,
      );
    } catch (e) {
      AppLogger.error('❌ Error loading available time slots', category: LogCategory.ui, error: e);
      setState(() {
        _availableTimeSlots = [];
      });
    }
  }

  Future<void> _confirmReschedule() async {
    if (_selectedDate == null || _selectedTimeSlotId == null) return;

    try {
      setState(() {
        _isLoading = true;
      });

      final employeeId = _getSelectedEmployeeId();

      AppLogger.info(
        '🔄 Rescheduling appointment to: ${_formatDate(_selectedDate!)} - Time Slot: $_selectedTimeSlotId - Employee: $employeeId',
        category: LogCategory.ui,
      );

      // تحديث الحجز مع employee_id الجديد
      final updateData = {
        'appointment_date': _selectedDate!.toIso8601String().split('T')[0],
        'time_slot_id': _selectedTimeSlotId,
        'status': 'confirmed',
      };

      // إضافة employee_id إذا كان متاحاً
      if (employeeId != null) {
        updateData['employee_id'] = employeeId;
        AppLogger.info(
          '👨‍⚕️ Updating appointment with new employee: $employeeId',
          category: LogCategory.ui,
        );
      }

      await _supabase
          .from('appointments')
          .update(updateData)
          .eq('id', widget.appointment.id);

      AppLogger.info('✅ Appointment rescheduled successfully with updated employee info', category: LogCategory.ui);

      Navigator.of(context).pop();
      widget.onReschedule();
    } catch (e) {
      AppLogger.error('❌ Error rescheduling appointment', category: LogCategory.ui, error: e);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في تغيير موعد الحجز'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}