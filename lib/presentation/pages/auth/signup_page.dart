import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/constants/medical_constants.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/helpers.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_events.dart';
import '../../bloc/auth/auth_states.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/custom_loading.dart';
import '../main/main_page.dart';
import 'privacy_policy_page.dart';

/// صفحة إنشاء حساب جديد - IIHC
class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  DateTime? _selectedBirthDate;
  String? _selectedGender;
  final List<String> _selectedTreatmentTypes = [];
  bool _acceptPrivacyPolicy = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  final List<String> _genderOptions = ['ذكر', 'أنثى'];
  
  // أنواع العلاج المتاحة من قاعدة البيانات
  final List<String> _availableTreatmentTypes = [
    'hearing',
    'speech', 
    'behavior',
    'motor',
    'sensory',
    'learning'
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _selectBirthDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 365 * 20)),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedBirthDate) {
      setState(() {
        _selectedBirthDate = picked;
      });
    }
  }

  void _signUp() {
    if (_formKey.currentState!.validate()) {
      if (!_acceptPrivacyPolicy) {
        Helpers.showErrorSnackBar(
          context, 
          'يجب الموافقة على سياسة الخصوصية للمتابعة'
        );
        return;
      }

      if (_selectedBirthDate == null) {
        Helpers.showErrorSnackBar(context, 'يرجى اختيار تاريخ الميلاد');
        return;
      }

      if (_selectedGender == null) {
        Helpers.showErrorSnackBar(context, 'يرجى اختيار الجنس');
        return;
      }

      if (_selectedTreatmentTypes.isEmpty) {
        Helpers.showErrorSnackBar(context, 'يرجى اختيار نوع العلاج المطلوب');
        return;
      }

      context.read<AuthBloc>().add(
        SignUpRequested(
          name: _nameController.text.trim(),
          email: _emailController.text.trim(),
          phone: _phoneController.text.trim(),
          password: _passwordController.text,
          birthDate: _selectedBirthDate!,
          gender: _selectedGender!,
          treatmentTypes: _selectedTreatmentTypes,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'إنشاء حساب جديد',
          style: TextStyle(
            color: AppColors.textPrimary,
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: BlocConsumer<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthSuccess) {
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const MainPage()),
                (route) => false,
              );
            } else if (state is AuthFailure) {
              Helpers.showErrorSnackBar(context, state.message);
            }
          },
          builder: (context, state) {
            return SingleChildScrollView(
              padding: EdgeInsets.all(24.w),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    SizedBox(height: 20.h),

                    // شعار التطبيق
                    _buildLogo(),

                    SizedBox(height: 32.h),

                    // حقل الاسم
                    FloatingLabelTextField(
                      controller: _nameController,
                      label: AppStrings.name,
                      hint: 'أدخل اسمك الكامل',
                      prefixIcon: Icons.person_outline,
                      validator: Validators.validateName,
                      enabled: state is! AuthLoading,
                    ),

                    SizedBox(height: 16.h),

                    // حقل البريد الإلكتروني
                    FloatingLabelTextField(
                      controller: _emailController,
                      label: AppStrings.email,
                      hint: 'أدخل بريدك الإلكتروني',
                      keyboardType: TextInputType.emailAddress,
                      prefixIcon: Icons.email_outlined,
                      validator: Validators.validateEmail,
                      enabled: state is! AuthLoading,
                    ),

                    SizedBox(height: 16.h),

                    // حقل رقم الهاتف
                    FloatingLabelTextField(
                      controller: _phoneController,
                      label: AppStrings.phone,
                      hint: 'أدخل رقم هاتفك',
                      keyboardType: TextInputType.phone,
                      prefixIcon: Icons.phone_outlined,
                      validator: Validators.validatePhone,
                      enabled: state is! AuthLoading,
                    ),

                    SizedBox(height: 16.h),

                    // تاريخ الميلاد
                    _buildBirthDateField(),

                    SizedBox(height: 16.h),

                    // الجنس
                    _buildGenderField(),

                    SizedBox(height: 16.h),

                    // كلمة المرور
                    FloatingLabelTextField(
                      controller: _passwordController,
                      label: AppStrings.password,
                      hint: 'أدخل كلمة المرور',
                      prefixIcon: Icons.lock_outline,
                      obscureText: _obscurePassword,
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscurePassword ? Icons.visibility : Icons.visibility_off,
                          color: AppColors.textSecondary,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                      validator: Validators.validatePassword,
                      enabled: state is! AuthLoading,
                    ),

                    SizedBox(height: 16.h),

                    // تأكيد كلمة المرور
                    FloatingLabelTextField(
                      controller: _confirmPasswordController,
                      label: AppStrings.confirmPassword,
                      hint: 'أعد إدخال كلمة المرور',
                      prefixIcon: Icons.lock_outline,
                      obscureText: _obscureConfirmPassword,
                      suffixIcon: IconButton(
                        icon: Icon(
                          _obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                          color: AppColors.textSecondary,
                        ),
                        onPressed: () {
                          setState(() {
                            _obscureConfirmPassword = !_obscureConfirmPassword;
                          });
                        },
                      ),
                      validator: (value) {
                        if (value != _passwordController.text) {
                          return AppStrings.passwordsNotMatch;
                        }
                        return null;
                      },
                      enabled: state is! AuthLoading,
                    ),

                    SizedBox(height: 16.h),

                    // أنواع العلاج
                    _buildTreatmentTypesField(),

                    SizedBox(height: 24.h),

                    // موافقة سياسة الخصوصية
                    _buildPrivacyPolicyCheckbox(),

                    SizedBox(height: 32.h),

                    // زر إنشاء الحساب
                    if (state is AuthLoading)
                      const CustomLoading(
                        message: 'جاري إنشاء الحساب...',
                        size: 40,
                      )
                    else
                      CustomButton(
                        text: 'إنشاء حساب',
                        onPressed: _signUp,
                      ),

                    SizedBox(height: 24.h),

                    // رابط تسجيل الدخول
                    _buildLoginLink(),

                    SizedBox(height: 20.h),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return Center(
      child: Container(
        width: 100.w,
        height: 100.h,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.primary.withValues(alpha: 0.1),
          border: Border.all(
            color: AppColors.primary.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: Icon(
          Icons.person_add_rounded,
          size: 50.sp,
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildBirthDateField() {
    return GestureDetector(
      onTap: _selectBirthDate,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.border),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today_outlined, color: AppColors.primary),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'تاريخ الميلاد',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    _selectedBirthDate != null
                        ? '${_selectedBirthDate!.day}/${_selectedBirthDate!.month}/${_selectedBirthDate!.year}'
                        : 'اختر تاريخ الميلاد',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: _selectedBirthDate != null
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenderField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الجنس',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 8.h),
        Row(
          children: _genderOptions.map((gender) {
            return Expanded(
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedGender = gender;
                  });
                },
                child: Container(
                  margin: EdgeInsets.only(right: gender == _genderOptions.first ? 0 : 8.w),
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: _selectedGender == gender
                          ? AppColors.primary
                          : AppColors.border,
                      width: _selectedGender == gender ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(12.r),
                    color: _selectedGender == gender
                        ? AppColors.primary.withValues(alpha: 0.1)
                        : Colors.transparent,
                  ),
                  child: Center(
                    child: Text(
                      gender,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: _selectedGender == gender
                            ? AppColors.primary
                            : AppColors.textPrimary,
                        fontWeight: _selectedGender == gender
                            ? FontWeight.w600
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildTreatmentTypesField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع العلاج المطلوب',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          'يمكنك اختيار أكثر من نوع',
          style: TextStyle(
            fontSize: 12.sp,
            color: AppColors.textSecondary,
          ),
        ),
        SizedBox(height: 12.h),
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: _availableTreatmentTypes.map((type) {
            final isSelected = _selectedTreatmentTypes.contains(type);
            final displayName = MedicalConstants.getTreatmentTypeText(type);

            return GestureDetector(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _selectedTreatmentTypes.remove(type);
                  } else {
                    _selectedTreatmentTypes.add(type);
                  }
                });
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: isSelected ? AppColors.primary : AppColors.border,
                    width: isSelected ? 2 : 1,
                  ),
                  borderRadius: BorderRadius.circular(20.r),
                  color: isSelected
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
                ),
                child: Text(
                  displayName,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: isSelected ? AppColors.primary : AppColors.textPrimary,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPrivacyPolicyCheckbox() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: _acceptPrivacyPolicy,
          onChanged: (value) {
            setState(() {
              _acceptPrivacyPolicy = value ?? false;
            });
          },
          activeColor: AppColors.primary,
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _acceptPrivacyPolicy = !_acceptPrivacyPolicy;
              });
            },
            child: Padding(
              padding: EdgeInsets.only(top: 12.h),
              child: RichText(
                text: TextSpan(
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textPrimary,
                    height: 1.4,
                  ),
                  children: [
                    const TextSpan(text: 'أوافق على '),
                    WidgetSpan(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const PrivacyPolicyPage(),
                            ),
                          );
                        },
                        child: Text(
                          'سياسة الخصوصية',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ),
                    const TextSpan(text: ' الخاصة بالتطبيق'),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginLink() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          'لديك حساب بالفعل؟ ',
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
          ),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'تسجيل الدخول',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
