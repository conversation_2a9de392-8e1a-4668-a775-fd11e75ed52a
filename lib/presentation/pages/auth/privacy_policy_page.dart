import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';

/// صفحة سياسة الخصوصية
class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          'سياسة الخصوصية',
          style: TextStyle(
            color: AppColors.textPrimary,
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(24.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              'مقدمة',
              'نحن في تطبيق "صحتي" نقدر خصوصيتك ونلتزم بحماية بياناتك الشخصية. توضح هذه السياسة كيفية جمع واستخدام وحماية المعلومات التي تقدمها عند استخدام تطبيقنا.',
            ),
            
            _buildSection(
              'المعلومات التي نجمعها',
              '''• الاسم الكامل: لتحديد هويتك في النظام
• رقم الهاتف: للتواصل وإرسال الإشعارات
• البريد الإلكتروني: لإنشاء الحساب والتواصل
• تاريخ الميلاد: لحساب العمر والخدمات المناسبة
• الجنس: لتقديم الرعاية الطبية المناسبة
• التاريخ المرضي: لتقديم أفضل رعاية طبية
• الأدوية الحالية: لتجنب التداخلات الدوائية
• الحساسية: لضمان سلامتك أثناء العلاج''',
            ),

            _buildSection(
              'كيف نستخدم معلوماتك',
              '''• حجز وإدارة المواعيد الطبية
• متابعة الحالة الصحية
• تقديم الاستشارات الطبية
• إرسال تذكيرات المواعيد
• إرسال إشعارات مهمة حول مواعيدك
• تأكيد الحجوزات والتغييرات
• إرسال نتائج الفحوصات''',
            ),

            _buildSection(
              'مشاركة المعلومات',
              '''نحن لا نشارك معلوماتك مع أطراف ثالثة إلا في الحالات التالية:
• الفريق الطبي: مع الأطباء والممرضين المعنيين بحالتك
• المختبرات الطبية: لإجراء الفحوصات المطلوبة
• شركات التأمين: بموافقتك المسبقة فقط
• الجهات القانونية: عند الطلب القانوني فقط''',
            ),

            _buildSection(
              'حماية البيانات',
              '''• جميع البيانات مشفرة أثناء النقل والتخزين
• استخدام بروتوكولات أمان متقدمة (SSL/TLS)
• تشفير قواعد البيانات بمعايير عالمية
• وصول محدود للموظفين المخولين فقط
• مراجعة دورية لصلاحيات الوصول
• تسجيل جميع عمليات الوصول للبيانات''',
            ),

            _buildSection(
              'حقوقك',
              '''لديك الحق في:
• الوصول: طلب نسخة من بياناتك الشخصية
• التصحيح: تعديل أي معلومات غير صحيحة
• الحذف: طلب حذف بياناتك (مع مراعاة المتطلبات الطبية)
• النقل: الحصول على بياناتك بصيغة قابلة للنقل
• الاعتراض: رفض استخدام بياناتك لأغراض معينة''',
            ),

            _buildSection(
              'الاحتفاظ بالبيانات',
              '''• البيانات الطبية: نحتفظ بها وفقاً للقوانين الطبية (عادة 7-10 سنوات)
• بيانات الحساب: حتى إغلاق الحساب أو طلب الحذف
• سجلات الاستخدام: لمدة سنتين لأغراض الأمان والتحسين''',
            ),

            _buildSection(
              'التواصل معنا',
              '''إذا كان لديك أي أسئلة حول سياسة الخصوصية:

البريد الإلكتروني: <EMAIL>
الهاتف: متاح عبر التطبيق
العنوان: تطبيق صحتي للرعاية الصحية''',
            ),

            SizedBox(height: 24.h),

            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: AppColors.primary.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Text(
                'باستخدام تطبيقنا، فإنك توافق على هذه السياسة وعلى جمع واستخدام معلوماتك كما هو موضح أعلاه.',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.primary,
                  fontWeight: FontWeight.w500,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            SizedBox(height: 16.h),

            Text(
              'تاريخ آخر تحديث: 3 أغسطس 2025',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 24.h),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        SizedBox(height: 12.h),
        Text(
          content,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textPrimary,
            height: 1.6,
          ),
        ),
        SizedBox(height: 24.h),
      ],
    );
  }
}
