import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/constants/app_colors.dart';
import '../../../core/utils/helpers.dart';
import '../../../core/utils/validators.dart';
import '../../../core/services/password_reset_service.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/custom_loading.dart';
import 'login_page.dart';

/// صفحة إنشاء كلمة مرور جديدة
class NewPasswordPage extends StatefulWidget {
  final String email;
  final String verificationCode;

  const NewPasswordPage({
    super.key,
    required this.email,
    required this.verificationCode,
  });

  @override
  State<NewPasswordPage> createState() => _NewPasswordPageState();
}

class _NewPasswordPageState extends State<NewPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Future<void> _resetPassword() async {
    if (_isLoading) return;

    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final result = await PasswordResetService.verifyCodeAndResetPassword(
          email: widget.email,
          token: widget.verificationCode,
          newPassword: _passwordController.text,
        );

        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          if (result.isSuccess) {
            // عرض رسالة نجاح
            Helpers.showSuccessSnackBar(
              context,
              'تم تغيير كلمة المرور بنجاح! يمكنك الآن تسجيل الدخول',
            );

            // الانتقال لصفحة تسجيل الدخول
            Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(
                builder: (context) => LoginPage(
                  initialEmail: widget.email,
                  showPasswordResetSuccess: true,
                ),
              ),
              (route) => false,
            );
          } else {
            Helpers.showErrorSnackBar(context, result.message);
          }
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          Helpers.showErrorSnackBar(context, 'حدث خطأ غير متوقع. حاول مرة أخرى.');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(24.w),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                SizedBox(height: 40.h),

                // أيقونة القفل
                _buildLockIcon(),

                SizedBox(height: 32.h),

                // العنوان
                _buildTitle(),

                SizedBox(height: 16.h),

                // الوصف
                _buildDescription(),

                SizedBox(height: 40.h),

                // حقل كلمة المرور الجديدة
                FloatingLabelTextField(
                  controller: _passwordController,
                  label: 'كلمة المرور الجديدة',
                  hint: 'أدخل كلمة مرور جديدة (8 خانات على الأقل)',
                  obscureText: _obscurePassword,
                  prefixIcon: Icons.lock_outlined,
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility_outlined
                          : Icons.visibility_off_outlined,
                      color: AppColors.textSecondary,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                  validator: Validators.validatePassword,
                  enabled: !_isLoading,
                ),

                SizedBox(height: 20.h),

                // حقل تأكيد كلمة المرور
                FloatingLabelTextField(
                  controller: _confirmPasswordController,
                  label: 'تأكيد كلمة المرور',
                  hint: 'أعد إدخال كلمة المرور الجديدة',
                  obscureText: _obscureConfirmPassword,
                  prefixIcon: Icons.lock_outlined,
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureConfirmPassword
                          ? Icons.visibility_outlined
                          : Icons.visibility_off_outlined,
                      color: AppColors.textSecondary,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureConfirmPassword = !_obscureConfirmPassword;
                      });
                    },
                  ),
                  validator: (value) => Validators.validateConfirmPassword(
                    value,
                    _passwordController.text,
                  ),
                  enabled: !_isLoading,
                  onSubmitted: (_) => _resetPassword(),
                ),

                SizedBox(height: 32.h),

                // زر تغيير كلمة المرور
                if (_isLoading)
                  const CustomLoading(
                    message: 'جاري تغيير كلمة المرور...',
                    size: 40,
                  )
                else
                  CustomButton(
                    text: 'تغيير كلمة المرور',
                    onPressed: _resetPassword,
                  ),

                SizedBox(height: 24.h),

                // معلومات الأمان
                _buildSecurityInfo(),

                SizedBox(height: 40.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLockIcon() {
    return Center(
      child: Container(
        width: 120.w,
        height: 120.h,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.success.withValues(alpha: 0.1),
          border: Border.all(
            color: AppColors.success.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child: Icon(
          Icons.lock_reset_rounded,
          size: 60.sp,
          color: AppColors.success,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Text(
      'إنشاء كلمة مرور جديدة',
      style: TextStyle(
        fontSize: 24.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildDescription() {
    return Text(
      'أدخل كلمة مرور جديدة قوية لحسابك. تأكد من أنها مختلفة عن كلمة المرور السابقة.',
      style: TextStyle(
        fontSize: 16.sp,
        color: AppColors.textSecondary,
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildSecurityInfo() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.info.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.info.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.security_rounded,
                color: AppColors.info,
                size: 20.sp,
              ),
              SizedBox(width: 12.w),
              Text(
                'نصائح لكلمة مرور قوية:',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.info,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          _buildSecurityTip('• استخدم 8 خانات على الأقل'),
          _buildSecurityTip('• امزج بين الأحرف والأرقام'),
          _buildSecurityTip('• تجنب المعلومات الشخصية'),
          _buildSecurityTip('• لا تشارك كلمة المرور مع أحد'),
        ],
      ),
    );
  }

  Widget _buildSecurityTip(String tip) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Align(
        alignment: Alignment.centerRight,
        child: Text(
          tip,
          style: TextStyle(
            fontSize: 13.sp,
            color: AppColors.info,
          ),
        ),
      ),
    );
  }
}
