import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  // تهيئة Supabase
  await Supabase.initialize(
    url: 'https://xqvdkdjnrcytswvfrkog.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhxdmRrZGpucmN5dHN3dmZya29nIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyMTI5MzAsImV4cCI6MjA2ODc4ODkzMH0.zaHSYMMK1QIRwCckgZXhT287rdW2IQUbY5Ag4U7PiRg',
  );

  final supabase = Supabase.instance.client;

  try {
    print('🔍 فحص جدول clinic_info...');
    
    // فحص البيانات الموجودة في الجدول
    final response = await supabase
        .from('clinic_info')
        .select('*')
        .limit(10);

    print('✅ تم العثور على ${response.length} سجل في جدول clinic_info');
    
    if (response.isNotEmpty) {
      print('\n📊 هيكل البيانات:');
      final firstRecord = response.first;
      firstRecord.forEach((key, value) {
        print('  $key: ${value?.runtimeType} = $value');
      });
      
      print('\n📋 جميع السجلات:');
      for (int i = 0; i < response.length; i++) {
        final record = response[i];
        print('\n--- السجل ${i + 1} ---');
        print('ID: ${record['id']}');
        print('Key: ${record['key']}');
        print('Value: ${record['value']}');
        print('Display Name: ${record['display_name']}');
        print('Info Type: ${record['info_type']}');
        print('Data Type: ${record['data_type']}');
        print('Icon Name: ${record['icon_name']}');
        print('Display Order: ${record['display_order']}');
        print('Is Active: ${record['is_active']}');
        print('Is Public: ${record['is_public']}');
      }
    } else {
      print('⚠️ لا توجد بيانات في جدول clinic_info');
      
      // إنشاء بيانات تجريبية
      print('\n🔧 إنشاء بيانات تجريبية...');
      
      final sampleData = [
        {
          'key': 'clinic_name',
          'value': 'عيادة السمع والنطق المتخصصة',
          'display_name': 'اسم العيادة',
          'info_type': 'general',
          'data_type': 'string',
          'icon_name': 'local_hospital',
          'display_order': 1,
          'is_active': true,
          'is_public': true,
          'description': 'الاسم الرسمي للعيادة'
        },
        {
          'key': 'clinic_phone',
          'value': '+966501234567',
          'display_name': 'هاتف العيادة',
          'info_type': 'contact',
          'data_type': 'phone',
          'icon_name': 'phone',
          'display_order': 2,
          'is_active': true,
          'is_public': true,
          'description': 'رقم الهاتف الرئيسي للعيادة'
        },
        {
          'key': 'clinic_email',
          'value': '<EMAIL>',
          'display_name': 'البريد الإلكتروني',
          'info_type': 'contact',
          'data_type': 'email',
          'icon_name': 'email',
          'display_order': 3,
          'is_active': true,
          'is_public': true,
          'description': 'البريد الإلكتروني الرسمي للعيادة'
        },
        {
          'key': 'clinic_address',
          'value': 'الرياض، المملكة العربية السعودية',
          'display_name': 'عنوان العيادة',
          'info_type': 'address',
          'data_type': 'string',
          'icon_name': 'location_on',
          'display_order': 4,
          'is_active': true,
          'is_public': true,
          'description': 'العنوان الكامل للعيادة'
        },
        {
          'key': 'working_hours',
          'value': 'السبت - الخميس: 8:00 ص - 8:00 م',
          'display_name': 'ساعات العمل',
          'info_type': 'working_hours',
          'data_type': 'string',
          'icon_name': 'schedule',
          'display_order': 5,
          'is_active': true,
          'is_public': true,
          'description': 'أوقات عمل العيادة'
        },
        {
          'key': 'emergency_phone',
          'value': '+966501234568',
          'display_name': 'هاتف الطوارئ',
          'info_type': 'contact',
          'data_type': 'phone',
          'icon_name': 'emergency',
          'display_order': 6,
          'is_active': true,
          'is_public': true,
          'description': 'رقم الطوارئ للحالات العاجلة'
        },
        {
          'key': 'website',
          'value': 'https://iihc-clinic.com',
          'display_name': 'الموقع الإلكتروني',
          'info_type': 'contact',
          'data_type': 'url',
          'icon_name': 'website',
          'display_order': 7,
          'is_active': true,
          'is_public': true,
          'description': 'الموقع الرسمي للعيادة'
        },
        {
          'key': 'whatsapp',
          'value': '+966501234567',
          'display_name': 'واتساب',
          'info_type': 'social_media',
          'data_type': 'phone',
          'icon_name': 'whatsapp',
          'display_order': 8,
          'is_active': true,
          'is_public': true,
          'description': 'رقم الواتساب للتواصل'
        }
      ];
      
      for (final data in sampleData) {
        await supabase.from('clinic_info').insert(data);
        print('✅ تم إدراج: ${data['display_name']}');
      }
      
      print('\n🎉 تم إنشاء البيانات التجريبية بنجاح!');
    }
    
  } catch (e) {
    print('❌ خطأ في الاتصال بقاعدة البيانات: $e');
  }
}
